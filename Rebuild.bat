@echo off

echo Cleaning build directory...
rd /s /q build

echo Configuring with CMake...
cmake -G "Visual Studio 17 2022" -A Win32 -B build -DCMAKE_TOOLCHAIN_FILE=C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Release

echo Generating proto file ...
cmake --build build

echo Building ...
cmake --build build
pause
@REM echo Run Test
@REM cd build\Debug
@REM Test.exe
cd ..
cd ..
﻿cmake_minimum_required(VERSION 3.15)
file(STRINGS "${CMAKE_CURRENT_SOURCE_DIR}/version.txt" projectVersion)
project(ECU_Fmu VERSION ${projectVersion} LANGUAGES C CXX)

if (POLICY CMP0091)
    cmake_policy(SET CMP0091 NEW)
endif ()
# Suppress warning for FindBoost on CMake >= 3.28
if(POLICY CMP0167)
    cmake_policy(SET CMP0167 OLD)
endif()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#If use for FMI 2.0
set(USE_FMI_2_0 OFF)

#Test Driver
set(TEST_LPSPI_DRIVER ON)

set(TEST_ADC_DRIVER ON)

set(TEST_LIN_DRIVER ON)

set(TEST_LIN_TRCV_DRIVER ON)
#Test MAIN
set(TEST_MAIN OFF)

# =================================================== END OF SELECTION =================================================

if (USE_FMI_2_0)
    add_definitions(-DFMI_2_0)
endif ()

if (TEST_LPSPI_DRIVER)
    add_definitions(-DSTUB_LPSPI)
endif()

if (TEST_ADC_DRIVER)
    add_definitions(-DTEST_ADC)
endif()

if (TEST_LIN_DRIVER)
    add_definitions(-DSTUB_LIN)
endif()

if (TEST_LIN_TRCV_DRIVER)
    add_definitions(-DSTUB_LIN_TRCV)
endif()

if (TEST_MAIN)
    add_definitions(-DSTUB_MAIN)
endif()
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

add_definitions(-D_WIN32_WINNT=0x0601 -DWIN32_LEAN_AND_MEAN)

set(BMS_LIB
${CMAKE_CURRENT_SOURCE_DIR}/bin
)
# Set binary directory
set(bin_dir "${CMAKE_CURRENT_SOURCE_DIR}/bin")

file(GLOB_RECURSE LIB_DIR 
${bin_dir}/*.dll
)

# Include path directories for BCC
set(BCC_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../vBMS_SW/BSW/CDD/BCC_Model")
get_filename_component(BCC_incl "${BCC_incl_path}" REALPATH)

set(SBC_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../vBMS_SW/BSW/CDD/SBC_Model")
get_filename_component(SBC_incl "${SBC_incl_path}" REALPATH)

# Include path directories for BCC
set(APP_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../vBMS_SW/ASW")
get_filename_component(APP_incl "${APP_incl_path}" REALPATH)

# Include path directories for MCAL
set(MCAL_incl_path "${CMAKE_CURRENT_SOURCE_DIR}/../vBMS_SW/BSW")
get_filename_component(MCAL_incl "${MCAL_incl_path}" REALPATH)

############ ModelIdentifier and export version ###########

set(modelIdentifier identity)
set(fmi_version fmi2) # fmi2 and fmi3

###########################################################

add_subdirectory(SystemC)
################ Protobuf implementation ##################

find_package(Boost REQUIRED COMPONENTS system)
find_package(Protobuf REQUIRED)

set(PROTO_DIR "${CMAKE_SOURCE_DIR}/proto")
set(bin_test "${CMAKE_BINARY_DIR}/Debug")

protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${PROTO_DIR}/message.proto)

include_directories(
    ${Boost_INCLUDE_DIRS}
    ${Protobuf_INCLUDE_DIRS}
    ${CMAKE_CURRENT_BINARY_DIR}  
)

file(GLOB_RECURSE Com 
${CMAKE_CURRENT_SOURCE_DIR}/Test/Com/Com_Lin/*.cpp
${CMAKE_CURRENT_SOURCE_DIR}/Test/Com/Com_Spi/*.cpp
)

file(GLOB_RECURSE Module
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/BCC_Module/*.cpp
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/SBC_Module/*.cpp
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/SW_Module/*.cpp
)

add_executable(Test Test/src/Test.cpp ${Com} ${Module} ${PROTO_SRCS} ${PROTO_HDRS})

foreach(DLL ${LIB_DIR})
    add_custom_command(TARGET Test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${DLL}
                ${bin_test}
    )
endforeach()

target_include_directories(Test PUBLIC
# Include Com
${CMAKE_CURRENT_SOURCE_DIR}/Test/Com/Com_Lin
${CMAKE_CURRENT_SOURCE_DIR}/Test/Com/Com_Spi
# Include Module
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/BCC_Module
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/SBC_Module
${CMAKE_CURRENT_SOURCE_DIR}/Test/Module/SW_Module
# Include APP
${APP_incl}/include
# Include directories for BCC
${BCC_incl}/Modeling
${BCC_incl}/include
# Include directories for SBC
${SBC_incl}/Lin_Adapter/include
${SBC_incl}/Lin_Trcv_fs23/include
# Include directories for MCAL
${MCAL_incl}/inc
${MCAL_incl}/board
${MCAL_incl}/generate/include
${MCAL_incl}/HAL/include
${MCAL_incl}/RTD/include
${MCAL_incl}/User_Config/include
${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/include
${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/header
${MCAL_incl}/Platform_TS_T40D34M30I0R0/startup/include
${MCAL_incl}/Platform_TS_T40D34M30I0R0/include
)

target_link_libraries(Test PRIVATE
    Boost::system
    protobuf::libprotobuf
    SystemC::systemc
    ws2_32           
)

if(MSVC)
    target_link_libraries (Test PRIVATE   
        ${CMAKE_DL_LIBS} 
        "${BMS_LIB}/libBSW.lib"
        "${BMS_LIB}/libASW.lib" 
        # "${BMS_LIB}/libVINFAST_dll.lib"
        
    )
else()
    target_link_libraries (Test PRIVATE 
        ${CMAKE_DL_LIBS} 
        "${BMS_LIB}/libBSW.dll.a"
        "${BMS_LIB}/libASW.dll.a" 
        # "${BMS_LIB}/libVINFAST_dll.dll.a"
    )
endif()

#########################################################

add_subdirectory(Fmi)
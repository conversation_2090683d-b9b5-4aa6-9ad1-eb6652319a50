#include "Com_Spi.hpp"

// Hằng số cho SPI communication
const int BCC_FRAME_SIZE = 6;

/**
 * @brief Constructor của Com_Spi module
 * 
 * Khởi tạo:
 * - UDP socket trên port 9002 để nhận dữ liệu sensor
 * - Các SystemC threads cho UDP receive và SPI emulation
 * - Khởi tạo các flags điều khiển
 */
SC_CTOR(Com_Spi) : socket_(io_ctx, udp::endpoint(udp::v4(), 9002)) {
    // Khởi tạo flags
    get_sensor = false;
    udp_data_received = false;
    
    // Khởi tạo dữ liệu sensor với giá trị mặc định
    for (int i = 0; i < BCC_FRAME_SIZE; i++) {
        last_volt[i] = 0.0;
        last_thermal[i] = 0.0;
    }
    
    // Khởi tạo các SystemC threads
    SC_THREAD(udp_receive_thread);
    SC_THREAD(spi_emulation);
}

/**
 * @brief Thread nhận dữ liệu sensor từ UDP
 * 
 * Quy trình hoạt động:
 * 1. Chờ flag get_sensor được set từ SPI emulation
 * 2. Nhận dữ liệu AFEData từ UDP port 9002
 * 3. Parse voltage và temperature data
 * 4. Cập nhật vào BCC modeling functions
 * 5. Set flag udp_data_received để báo hoàn thành
 */
void Com_Spi::udp_receive_thread() {
    udp::endpoint sender_ep;
    std::array<char, 1024> recv_buf;
    
    while (1) {
        wait(1, SC_NS);  // Chờ 1 nanosecond
        
        // Chỉ xử lý khi được yêu cầu và chưa nhận dữ liệu
        if (get_sensor && !udp_data_received) {
            std::cout << sc_time_stamp() << " [COM_SPI] Đang chờ dữ liệu sensor từ UDP..." << std::endl;
            
            // Nhận dữ liệu từ UDP
            boost::system::error_code ec;
            size_t bytes = socket_.receive_from(boost::asio::buffer(recv_buf), sender_ep, 0, ec);
            
            if (!ec) {
                // Parse protobuf AFEData message
                example::AFEData msg;
                if (msg.ParseFromArray(recv_buf.data(), bytes)) {
                    
                    // Cập nhật dữ liệu voltage
                    for (size_t i = 0; i < BCC_FRAME_SIZE && i < msg.volt_size(); ++i) {
                        last_volt[i] = msg.volt(i);
                    }
                    
                    // Log dữ liệu voltage nhận được
                    std::cout << sc_time_stamp() << " [COM_SPI] Nhận dữ liệu Voltage: ";
                    for (int i = 0; i < BCC_FRAME_SIZE; i++) {
                        std::cout << last_volt[i] << "V ";
                    }
                    std::cout << std::endl;
                    
                    // Cập nhật dữ liệu temperature
                    for (size_t j = 0; j < BCC_FRAME_SIZE && j < msg.thermal_size(); ++j) {
                        last_thermal[j] = msg.thermal(j);
                    }
                    
                    // Log dữ liệu temperature nhận được
                    std::cout << sc_time_stamp() << " [COM_SPI] Nhận dữ liệu Temperature: ";
                    for (int i = 0; i < BCC_FRAME_SIZE; i++) {
                        std::cout << last_thermal[i] << "°C ";
                    }
                    std::cout << std::endl;
                    
                    // Đánh dấu đã nhận dữ liệu
                    udp_data_received = true;
                }
            }
            
            // Cập nhật dữ liệu vào BCC modeling
            set_cellsVoltage(last_volt);
            set_cellsTemp(last_thermal);
        }
    }
}

/**
 * @brief Thread mô phỏng giao tiếp SPI
 * 
 * Quy trình hoạt động:
 * 1. Chờ tín hiệu CS (Chip Select) được kích hoạt
 * 2. Yêu cầu dữ liệu sensor mới từ UDP
 * 3. Thực hiện trao đổi dữ liệu SPI với master (BMS)
 * 4. Gọi BCC_processing để xử lý frame nhận được
 */
void Com_Spi::spi_emulation() {
    while (1) {
        wait(1, SC_US);  // Chờ 1 microsecond
        
        // Kiểm tra tín hiệu Chip Select
        if (CS.read()) {
            std::cout << sc_time_stamp() << " [COM_SPI] CS được kích hoạt - Bắt đầu giao tiếp SPI" << std::endl;
            
            // Yêu cầu dữ liệu sensor mới
            get_sensor = true;
            udp_data_received = false;
            
            // Chờ dữ liệu UDP được nhận
            while (get_sensor && !udp_data_received) {
                wait(10, SC_NS);  // Chờ 10 nanoseconds
            }
            
            std::cout << sc_time_stamp() << " [COM_SPI] Bắt đầu trao đổi " << BCC_FRAME_SIZE << " bytes" << std::endl;
            
            // Trao đổi dữ liệu SPI frame (6 bytes)
            for (int i = 0; i < BCC_FRAME_SIZE; ++i) {
                // Chờ tín hiệu data_ready từ master
                while (!data_ready.read()) {
                    wait(data_ready->value_changed_event());
                }
                
                // Đọc dữ liệu từ master và gửi dữ liệu đến master
                rx_frame[i] = spi_rx.read();
                spi_tx.write(tx_frame[i]);
                
                std::cout << sc_time_stamp() << " [COM_SPI] Byte " << i 
                          << ": RX=0x" << std::hex << (unsigned int)rx_frame[i]
                          << ", TX=0x" << (unsigned int)tx_frame[i] << std::dec << std::endl;
                
                // Gửi ACK để báo hoàn thành
                ack.write(true);
                
                // Chờ data_ready được clear
                while (data_ready.read()) {
                    wait(data_ready->value_changed_event());
                }
                
                // Clear ACK
                ack.write(false);
            }
            
            std::cout << sc_time_stamp() << " [COM_SPI] Hoàn thành trao đổi SPI" << std::endl;
            
            // Reset flag
            get_sensor = false;
            
            // Gọi BCC processing để xử lý dữ liệu nhận được
            BCC_processing(tx_frame, rx_frame);
        }
    }
}
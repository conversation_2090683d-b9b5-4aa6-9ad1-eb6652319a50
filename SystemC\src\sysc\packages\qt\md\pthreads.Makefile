# Generated automatically from <PERSON><PERSON>le.in by configure.
# Makefile.in generated automatically by automake 1.4 from Makefile.am

# Copyright (C) 1994, 1995-8, 1999 Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.


SHELL = /bin/sh

all: 

mostlyclean-noinstLIBRARIES:

clean-noinstLIBRARIES:

distclean-noinstLIBRARIES:

maintainer-clean-noinstLIBRARIES:

mostlyclean-compile:

clean-compile:

distclean-compile:

maintainer-clean-compile:

tags: 

ID: 

TAGS:

mostlyclean-tags:

clean-tags:

distclean-tags:

maintainer-clean-tags:

distdir: 
	
info-am:
info: 
dvi-am:
dvi: dvi-am
check-am: all-am
check: check-am
installcheck-am:
installcheck: installcheck-am
install-exec-am:
install-exec: install-exec-am

install-data-am: install-data-local
install-data: install-data-am

install-am: all-am
install: install-am
uninstall-am: uninstall-local
uninstall: uninstall-am
all-am: 
all-redirect: 
install-strip:
installdirs:


mostlyclean-generic:

clean-generic:

distclean-generic:

maintainer-clean-generic:
mostlyclean-am: 

mostlyclean:

clean-am:

clean:

distclean-am:

distclean:

maintainer-clean-am:

maintainer-clean:

.PHONY:


configuration:

clean:

install-data-local:

uninstall-local:


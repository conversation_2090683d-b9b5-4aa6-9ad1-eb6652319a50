#include "BCC_Module.hpp"

/**
 * @brief Constructor của BCC_Module
 * 
 * Khởi tạo:
 * - Tạo instance của Com_Spi module
 * - Thiết lập các kết nối ports
 * - Khởi tạo các monitoring threads
 * - Thiết lập các biến trạng thái
 */
SC_CTOR(BCC_Module) {
    // Khởi tạo các biến trạng thái
    transaction_count = 0;
    module_active = false;
    
    // Tạo instance của Com_Spi module
    com_spi_inst = new Com_Spi("com_spi_instance");
    
    // Kết nối các ports của BCC_Module với Com_Spi
    initialize_connections();
    
    // Khởi tạo các monitoring và management threads
    SC_THREAD(monitor_spi_status);
    SC_THREAD(power_management);
    SC_THREAD(diagnostic_handler);
    
    std::cout << "[BCC_MODULE] BCC Module được khởi tạo thành công" << std::endl;
}

/**
 * @brief Destructor của BCC_Module
 * 
 * Giải phóng tài nguyên:
 * - Xóa instance của Com_Spi
 * - Cleanup các resources
 * - Log thống kê cuối cùng
 */
BCC_Module::~BCC_Module() {
    if (com_spi_inst) {
        std::cout << "[BCC_MODULE] Tổng số SPI transactions: " << transaction_count << std::endl;
        delete com_spi_inst;
        com_spi_inst = nullptr;
    }
    std::cout << "[BCC_MODULE] BCC Module được giải phóng" << std::endl;
}

/**
 * @brief Phương thức khởi tạo và kết nối các components
 * 
 * Chức năng:
 * - Kết nối trực tiếp các ports của BCC_Module với Com_Spi
 * - Thiết lập signal routing cho SPI communication
 * - Đảm bảo tính toàn vẹn của kết nối
 */
void BCC_Module::initialize_connections() {
    // Kết nối các ports SPI control
    com_spi_inst->CS(CS);               // Kết nối Chip Select
    com_spi_inst->ack(ack);             // Kết nối ACK signal
    
    // Kết nối các ports SPI data
    com_spi_inst->spi_tx(spi_tx);       // Kết nối SPI TX (slave to master)
    com_spi_inst->spi_rx(spi_rx);       // Kết nối SPI RX (master to slave)
    
    // Kết nối port điều khiển
    com_spi_inst->data_ready(data_ready); // Kết nối data ready signal
    
    std::cout << "[BCC_MODULE] Các kết nối SPI ports đã được thiết lập" << std::endl;
}

/**
 * @brief Phương thức giám sát và quản lý trạng thái SPI bus
 * 
 * Chức năng:
 * - Theo dõi hoạt động của SPI communication
 * - Đếm số lượng transactions
 * - Phát hiện và báo cáo lỗi
 * - Cung cấp thông tin diagnostic
 */
void BCC_Module::monitor_spi_status() {
    bool previous_cs = false;
    bool previous_data_ready = false;
    bool transaction_in_progress = false;
    sc_time transaction_start_time;
    
    while (true) {
        wait(10, SC_US);  // Kiểm tra mỗi 10 microseconds
        
        // Theo dõi tín hiệu Chip Select
        bool current_cs = CS.read();
        if (current_cs != previous_cs) {
            if (current_cs) {
                // CS được kích hoạt - bắt đầu transaction
                transaction_in_progress = true;
                transaction_start_time = sc_time_stamp();
                module_active = true;
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] SPI Transaction #" << (transaction_count + 1) 
                          << " bắt đầu" << std::endl;
            } else {
                // CS được deactivate - kết thúc transaction
                if (transaction_in_progress) {
                    transaction_count++;
                    sc_time transaction_duration = sc_time_stamp() - transaction_start_time;
                    std::cout << sc_time_stamp() 
                              << " [BCC_MODULE] SPI Transaction #" << transaction_count 
                              << " hoàn thành (Duration: " << transaction_duration << ")" << std::endl;
                    transaction_in_progress = false;
                }
            }
            previous_cs = current_cs;
        }
        
        // Theo dõi tín hiệu data_ready
        bool current_data_ready = data_ready.read();
        if (current_data_ready != previous_data_ready) {
            if (current_data_ready && transaction_in_progress) {
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Data ready signal detected" << std::endl;
            }
            previous_data_ready = current_data_ready;
        }
        
        // Báo cáo định kỳ về performance
        static int report_counter = 0;
        report_counter++;
        if (report_counter >= 10000) {  // Báo cáo mỗi 100ms (10000 * 10us)
            if (transaction_count > 0) {
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Performance Report - Total transactions: " 
                          << transaction_count << ", Module active: " 
                          << (module_active ? "Yes" : "No") << std::endl;
            }
            report_counter = 0;
            module_active = false;  // Reset cho chu kỳ tiếp theo
        }
    }
}

/**
 * @brief Phương thức quản lý power và performance
 * 
 * Chức năng:
 * - Quản lý power states của BCC
 * - Tối ưu hóa performance dựa trên workload
 * - Xử lý sleep/wake modes
 * - Điều chỉnh clock frequencies
 */
void BCC_Module::power_management() {
    enum PowerState { SLEEP, IDLE, ACTIVE, HIGH_PERFORMANCE };
    PowerState current_state = IDLE;
    int idle_counter = 0;
    
    while (true) {
        wait(1, SC_MS);  // Kiểm tra mỗi 1 millisecond
        
        // Xác định power state dựa trên hoạt động
        if (module_active) {
            if (current_state != ACTIVE && current_state != HIGH_PERFORMANCE) {
                current_state = ACTIVE;
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Power state: ACTIVE" << std::endl;
            }
            idle_counter = 0;
        } else {
            idle_counter++;
            
            // Chuyển sang IDLE sau 10ms không hoạt động
            if (idle_counter > 10 && current_state == ACTIVE) {
                current_state = IDLE;
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Power state: IDLE" << std::endl;
            }
            
            // Chuyển sang SLEEP sau 100ms không hoạt động
            if (idle_counter > 100 && current_state == IDLE) {
                current_state = SLEEP;
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Power state: SLEEP" << std::endl;
            }
        }
        
        // Điều chỉnh performance dựa trên transaction frequency
        static int perf_check_counter = 0;
        perf_check_counter++;
        if (perf_check_counter >= 1000) {  // Kiểm tra mỗi 1 giây
            if (transaction_count > 100) {  // High frequency transactions
                if (current_state == ACTIVE) {
                    current_state = HIGH_PERFORMANCE;
                    std::cout << sc_time_stamp() 
                              << " [BCC_MODULE] Power state: HIGH_PERFORMANCE" << std::endl;
                }
            }
            perf_check_counter = 0;
        }
    }
}

/**
 * @brief Phương thức xử lý diagnostic và error handling
 * 
 * Chức năng:
 * - Phát hiện lỗi communication
 * - Xử lý error recovery
 * - Cung cấp diagnostic data
 * - Monitor signal integrity
 */
void BCC_Module::diagnostic_handler() {
    int error_count = 0;
    int timeout_count = 0;
    bool communication_healthy = true;
    
    while (true) {
        wait(50, SC_US);  // Kiểm tra mỗi 50 microseconds
        
        // Kiểm tra timeout cho SPI transactions
        static bool cs_active_detected = false;
        static sc_time cs_start_time;
        
        if (CS.read() && !cs_active_detected) {
            cs_active_detected = true;
            cs_start_time = sc_time_stamp();
        } else if (!CS.read() && cs_active_detected) {
            cs_active_detected = false;
        } else if (cs_active_detected) {
            // Kiểm tra timeout (transaction quá 1ms)
            sc_time elapsed = sc_time_stamp() - cs_start_time;
            if (elapsed > sc_time(1, SC_MS)) {
                timeout_count++;
                error_count++;
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] WARNING: SPI transaction timeout detected (" 
                          << elapsed << ")" << std::endl;
                cs_active_detected = false;  // Reset để tránh spam
            }
        }
        
        // Kiểm tra signal integrity
        static bool prev_ack = false;
        bool curr_ack = ack.read();
        if (curr_ack != prev_ack) {
            // ACK signal thay đổi - đây là hoạt động bình thường
            prev_ack = curr_ack;
        }
        
        // Đánh giá tình trạng communication
        static int health_check_counter = 0;
        health_check_counter++;
        if (health_check_counter >= 20000) {  // Kiểm tra mỗi 1 giây (20000 * 50us)
            bool prev_health = communication_healthy;
            communication_healthy = (error_count < 10);  // Cho phép tối đa 10 lỗi/giây
            
            if (prev_health != communication_healthy) {
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Communication health changed: " 
                          << (communication_healthy ? "HEALTHY" : "DEGRADED") << std::endl;
            }
            
            // Reset counters
            error_count = 0;
            health_check_counter = 0;
            
            // Báo cáo diagnostic summary
            if (timeout_count > 0) {
                std::cout << sc_time_stamp() 
                          << " [BCC_MODULE] Diagnostic Summary - Timeouts: " 
                          << timeout_count << std::endl;
            }
        }
    }
}
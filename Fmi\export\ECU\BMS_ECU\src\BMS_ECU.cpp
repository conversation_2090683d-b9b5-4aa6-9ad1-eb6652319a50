#include "BMS_ECU.hpp"
#include <iomanip>
#include <sstream>
#include "BCC_modeling.h"
#include "Lin_Adapter_Wrapper.h"
#include "BCC_Wrapper_Transmit.h"

BMS_ECU::BMS_ECU(const fmu_data& data) : fmu_base(data) {
    // Register FMU variables
    register_variable(
        real("outbms", &outbms_)
        .setCausality(causality_t::OUTPUT)
        .setVariability(variability_t::CONTINUOUS)
        .setInitial(initial_t::EXACT));

    register_variable(
        real("Voltage", &Volt_)
        .setCausality(causality_t::INPUT)
        .setVariability(variability_t::CONTINUOUS));

    register_variable(
        string("Lin_frame", &lin_frame_)
        .setCausality(causality_t::INPUT)
        .setVariability(variability_t::DISCRETE));

    // Instantiate SystemC modules
    bms = new BMS_Software("bms");
    afe = new AFE("afe");
    sbc = new SBC("sbc");

    // Connect signals
    bms->in1.bind(sig_in1);
    bms->CS.bind(sig_CS);
    bms->sig_bool_AFE.bind(sig_bool_afe);
    bms->in2.bind(sig_in2);
    bms->sig_bool_SBC.bind(sig_bool_sbc);
    bms->out_BMS.bind(out_bms);

    afe->bool_in.bind(sig_bool_afe);
    afe->sig_in1.bind(sig_in1);
    afe->bool_CS.bind(sig_CS);
    afe->voltage_in.bind(sig_voltage);

    sbc->bool_in.bind(sig_bool_sbc);
    sbc->sig_in2.bind(sig_in2);

    bms->init_CDD();

    reset();
}

bool BMS_ECU::do_step(double dt) {
    sig_voltage.write(Volt_);

    sc_time step_time(dt * 500, SC_US);
    sc_start(step_time);
    outbms_ = out_bms.read();

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2)
        << ", BMS Output: " << out_bms.read();
    log(fmiOK, oss.str());

    std::cout << "[INFO_BMS] BMS Output: " << out_bms.read() << " at time: " << dt * 500 << std::endl;

    return true;
}

void BMS_ECU::reset() {
    outbms_ = 0;
}

void BMS_ECU::terminate() {
    sc_core::sc_stop();

    delete bms;
    delete afe;
    delete sbc;
}

void BMS_ECU::BMS_Software::SW_application() {
    while (true) {
        wait(1, SC_US);
        int sum = in1.read() + in2.read();
        out_BMS.write(sum);

        CS.write(true);
        // Call Spi to send

        wait(in1.value_changed_event());
        // Call Spi to receive    
    }
}

void BMS_ECU::BMS_Software::init_CDD() {
    sig_bool_AFE.write(true);
    sig_bool_SBC.write(true);
}

void BMS_ECU::AFE::compute_output() {
    while (true) {
        wait(3, SC_US);
        if (bool_in.read()) {
            // Set Dummy voltage values

            if (bool_CS.read()) {
                AFE_heartbeat =  (AFE_heartbeat + 1) % 100;
                sig_in1.write(AFE_heartbeat);
            }
        }
    }
}

void BMS_ECU::SBC::compute_output() {
    while (true) {
        wait(2, SC_US);

        if (bool_in.read()) {
            SBC_heartbeat = (SBC_heartbeat + 1) % 10;
            sig_in2.write(SBC_heartbeat);
        }
    }
}

model_info fmu4cpp::get_model_info() {
    model_info info;
    info.modelName = "BMS_ECU";
    info.description = "BMS ECU with SystemC integration";
    info.modelIdentifier = FMU4CPP_MODEL_IDENTIFIER;
    info.author = "SIL VietSol";
    return info;
}

FMU4CPP_INSTANTIATE(BMS_ECU);
#ifndef SW_MODULE_HPP
#define SW_MODULE_HPP

#include <systemc.h>
#include <iostream>
#include "Lin_Adapter_Wrapper.h"
#include "BCC_Wrapper_Transmit.h"
#include "Application_Wrapper.h"
#include "hal_bcc.h"

/**
 * @brief SW (Software) Module - Module phần mềm BMS chính
 * 
 * Module này chịu trách nhiệm:
 * - Xử lý logic nghiệp vụ chính của BMS
 * - Giao tiếp với SBC Module qua LIN protocol
 * - Giao tiếp với BCC Module qua SPI protocol
 * - Thực hiện các thuật toán quản lý pin
 * - Xử lý dữ liệu sensor và điều khiển
 * - <PERSON><PERSON><PERSON> vai trò như brain của toàn bộ hệ thống BMS
 */
SC_MODULE(SW_Module) {
    // ===== LIN COMMUNICATION PORTS =====
    // Ports kết nối với SBC Module
    sc_in<uint8_t> linrx;               // Nhận dữ liệu LIN từ SBC
    sc_out<uint8_t> lintx;              // Gửi dữ liệu LIN đến SBC
    sc_in<bool> data_ready;             // Tín hiệu dữ liệu sẵn sàng từ SBC
    sc_out<bool> ack_lin;               // ACK cho LIN communication
    
    // Handshaking ports với SBC
    sc_out<bool> bms_sbc_ready;         // BMS sẵn sàng gửi dữ liệu đến SBC
    sc_in<bool> bms_sbc_ack_lin;        // ACK từ SBC
    
    // ===== SPI COMMUNICATION PORTS =====
    // Ports kết nối với BCC Module
    sc_out<bool> CS;                    // Chip Select cho SPI
    sc_in<uint8_t> Spi_rx;              // Nhận dữ liệu SPI từ BCC
    sc_out<uint8_t> Spi_tx;             // Gửi dữ liệu SPI đến BCC
    sc_in<bool> ack_BCC;                // ACK từ BCC
    sc_out<bool> data_ready_BCC;        // Data ready cho BCC
    
    // ===== INTERNAL VARIABLES =====
    // LIN frame buffers
    uint8_t bms_lintxframe[9];          // Buffer cho LIN TX frame
    uint8_t bms_linrxframe[11];         // Buffer cho LIN RX frame (full frame)
    
    // SPI frame buffers
    uint8_t BCC_spitxframe[6];          // Buffer cho SPI TX frame
    uint8_t BCC_spirxframe[6];          // Buffer cho SPI RX frame
    
    // Application data
    double cells_volt;                  // Giá trị voltage của cells
    
    // State management
    bool system_initialized;            // Trạng thái khởi tạo hệ thống
    int cycle_count;                    // Đếm số chu kỳ xử lý
    
    // ===== METHODS =====
    
    /**
     * @brief Thread chính xử lý logic BMS
     * 
     * Quy trình hoạt động:
     * 1. Nhận dữ liệu LIN từ SBC
     * 2. Xử lý payload LIN
     * 3. Thực hiện giao tiếp SPI với BCC
     * 4. Xử lý dữ liệu sensor
     * 5. Chạy thuật toán BMS
     * 6. Gửi response LIN về SBC
     */
    void main_thread();
    
    /**
     * @brief Phương thức thực hiện SPI transfer
     * 
     * @param txframe Buffer chứa dữ liệu gửi
     * @param frame_size Kích thước frame (bytes)
     * @param rx_frame Buffer nhận dữ liệu
     * 
     * Chức năng:
     * - Kích hoạt CS signal
     * - Thực hiện handshaking với BCC
     * - Trao đổi dữ liệu SPI
     * - Deactivate CS signal
     */
    void spi_transfer(uint8_t* txframe, uint8_t frame_size, uint8_t* rx_frame);
    
    /**
     * @brief Phương thức xử lý LIN communication
     * 
     * Chức năng:
     * - Nhận full LIN frame từ SBC
     * - Parse và validate dữ liệu
     * - Extract payload data
     */
    void process_lin_input();
    
    /**
     * @brief Phương thức chuẩn bị LIN response
     * 
     * Chức năng:
     * - Tạo LIN response frame
     * - Gửi dữ liệu đến SBC
     * - Thực hiện handshaking
     */
    void send_lin_response();
    
    /**
     * @brief Phương thức xử lý BCC operations
     * 
     * Chức năng:
     * - Đọc/ghi BCC registers
     * - Cấu hình ADC
     * - Đọc dữ liệu measurement
     * - Xử lý dữ liệu sensor
     */
    void process_bcc_operations();
    
    /**
     * @brief Phương thức chạy application logic
     * 
     * Chức năng:
     * - Thực hiện thuật toán quản lý pin
     * - Xử lý safety functions
     * - Cập nhật trạng thái hệ thống
     * - Tính toán các thông số BMS
     */
    void run_application_logic();
    
    /**
     * @brief Phương thức giám sát và diagnostic
     * 
     * Chức năng:
     * - Theo dõi performance
     * - Phát hiện lỗi
     * - Báo cáo trạng thái
     */
    void system_monitor();
    
    // Constructor
    SC_CTOR(SW_Module);
};

#endif // SW_MODULE_HPP
/*****************************************************************************
  
  Licensed to Accellera Systems Initiative Inc. (Accellera) under one or
  more contributor license agreements.  See the NOTICE file distributed
  with this work for additional information regarding copyright ownership.
  Accellera licenses this file to you under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with the
  License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
  implied.  See the License for the specific language governing
  permissions and limitations under the License.

 *****************************************************************************/

/*****************************************************************************

  sc_int64_mask.cpp -- Fills the mask_int lookup table to enable efficient
                       part-selection on 64-bit sc_ints and sc_uints.

  Original Author: Amit Rao, Synopsys, Inc.

 *****************************************************************************/

/*****************************************************************************

  MODIFICATION LOG - modifiers, enter your name, affiliation, date and
  changes you are making here.

      Name, Affiliation, Date: Ali Dasdan, Synopsys, Inc.
  Description of Modification: - Resolved ambiguity with sc_(un)signed.
                               - Merged the code for 64- and 32-bit versions
                                 via the constants in sc_nbdefs.h.
                               - Eliminated redundant file inclusions.

      Name, Affiliation, Date:
  Description of Modification:

 *****************************************************************************/


// $Log: sc_int64_mask.cpp,v $
// Revision 1.3  2011/02/18 20:19:14  acg
//  Andy Goodrich: updating Copyright notice.
//
// Revision 1.2  2009/05/22 16:06:29  acg
//  Andy Goodrich: process control updates.
//
// Revision *******  2006/12/15 20:20:05  acg
// SystemC 2.3
//
// Revision 1.3  2006/01/13 18:49:31  acg
// Added $Log command so that CVS check in comments are reproduced in the
// source.
//

#include "sysc/datatypes/int/sc_int_base.h"
#include "sysc/datatypes/int/sc_uint_base.h"


namespace sc_dt
{

// 64-bit sign extensions masks.

const uint_type mask_int[SC_INTWIDTH][SC_INTWIDTH] = 
{
{
0xfffffffffffffffeULL
},
{
0xfffffffffffffffcULL,
0xfffffffffffffffdULL
},
{
0xfffffffffffffff8ULL,
0xfffffffffffffff9ULL,
0xfffffffffffffffbULL
},
{
0xfffffffffffffff0ULL,
0xfffffffffffffff1ULL,
0xfffffffffffffff3ULL,
0xfffffffffffffff7ULL
},
{
0xffffffffffffffe0ULL,
0xffffffffffffffe1ULL,
0xffffffffffffffe3ULL,
0xffffffffffffffe7ULL,
0xffffffffffffffefULL
},
{
0xffffffffffffffc0ULL,
0xffffffffffffffc1ULL,
0xffffffffffffffc3ULL,
0xffffffffffffffc7ULL,
0xffffffffffffffcfULL,
0xffffffffffffffdfULL
},
{
0xffffffffffffff80ULL,
0xffffffffffffff81ULL,
0xffffffffffffff83ULL,
0xffffffffffffff87ULL,
0xffffffffffffff8fULL,
0xffffffffffffff9fULL,
0xffffffffffffffbfULL
},
{
0xffffffffffffff00ULL,
0xffffffffffffff01ULL,
0xffffffffffffff03ULL,
0xffffffffffffff07ULL,
0xffffffffffffff0fULL,
0xffffffffffffff1fULL,
0xffffffffffffff3fULL,
0xffffffffffffff7fULL
},
{
0xfffffffffffffe00ULL,
0xfffffffffffffe01ULL,
0xfffffffffffffe03ULL,
0xfffffffffffffe07ULL,
0xfffffffffffffe0fULL,
0xfffffffffffffe1fULL,
0xfffffffffffffe3fULL,
0xfffffffffffffe7fULL,
0xfffffffffffffeffULL
},
{
0xfffffffffffffc00ULL,
0xfffffffffffffc01ULL,
0xfffffffffffffc03ULL,
0xfffffffffffffc07ULL,
0xfffffffffffffc0fULL,
0xfffffffffffffc1fULL,
0xfffffffffffffc3fULL,
0xfffffffffffffc7fULL,
0xfffffffffffffcffULL,
0xfffffffffffffdffULL
},
{
0xfffffffffffff800ULL,
0xfffffffffffff801ULL,
0xfffffffffffff803ULL,
0xfffffffffffff807ULL,
0xfffffffffffff80fULL,
0xfffffffffffff81fULL,
0xfffffffffffff83fULL,
0xfffffffffffff87fULL,
0xfffffffffffff8ffULL,
0xfffffffffffff9ffULL,
0xfffffffffffffbffULL
},
{
0xfffffffffffff000ULL,
0xfffffffffffff001ULL,
0xfffffffffffff003ULL,
0xfffffffffffff007ULL,
0xfffffffffffff00fULL,
0xfffffffffffff01fULL,
0xfffffffffffff03fULL,
0xfffffffffffff07fULL,
0xfffffffffffff0ffULL,
0xfffffffffffff1ffULL,
0xfffffffffffff3ffULL,
0xfffffffffffff7ffULL
},
{
0xffffffffffffe000ULL,
0xffffffffffffe001ULL,
0xffffffffffffe003ULL,
0xffffffffffffe007ULL,
0xffffffffffffe00fULL,
0xffffffffffffe01fULL,
0xffffffffffffe03fULL,
0xffffffffffffe07fULL,
0xffffffffffffe0ffULL,
0xffffffffffffe1ffULL,
0xffffffffffffe3ffULL,
0xffffffffffffe7ffULL,
0xffffffffffffefffULL
},
{
0xffffffffffffc000ULL,
0xffffffffffffc001ULL,
0xffffffffffffc003ULL,
0xffffffffffffc007ULL,
0xffffffffffffc00fULL,
0xffffffffffffc01fULL,
0xffffffffffffc03fULL,
0xffffffffffffc07fULL,
0xffffffffffffc0ffULL,
0xffffffffffffc1ffULL,
0xffffffffffffc3ffULL,
0xffffffffffffc7ffULL,
0xffffffffffffcfffULL,
0xffffffffffffdfffULL
},
{
0xffffffffffff8000ULL,
0xffffffffffff8001ULL,
0xffffffffffff8003ULL,
0xffffffffffff8007ULL,
0xffffffffffff800fULL,
0xffffffffffff801fULL,
0xffffffffffff803fULL,
0xffffffffffff807fULL,
0xffffffffffff80ffULL,
0xffffffffffff81ffULL,
0xffffffffffff83ffULL,
0xffffffffffff87ffULL,
0xffffffffffff8fffULL,
0xffffffffffff9fffULL,
0xffffffffffffbfffULL
},
{
0xffffffffffff0000ULL,
0xffffffffffff0001ULL,
0xffffffffffff0003ULL,
0xffffffffffff0007ULL,
0xffffffffffff000fULL,
0xffffffffffff001fULL,
0xffffffffffff003fULL,
0xffffffffffff007fULL,
0xffffffffffff00ffULL,
0xffffffffffff01ffULL,
0xffffffffffff03ffULL,
0xffffffffffff07ffULL,
0xffffffffffff0fffULL,
0xffffffffffff1fffULL,
0xffffffffffff3fffULL,
0xffffffffffff7fffULL
},
{
0xfffffffffffe0000ULL,
0xfffffffffffe0001ULL,
0xfffffffffffe0003ULL,
0xfffffffffffe0007ULL,
0xfffffffffffe000fULL,
0xfffffffffffe001fULL,
0xfffffffffffe003fULL,
0xfffffffffffe007fULL,
0xfffffffffffe00ffULL,
0xfffffffffffe01ffULL,
0xfffffffffffe03ffULL,
0xfffffffffffe07ffULL,
0xfffffffffffe0fffULL,
0xfffffffffffe1fffULL,
0xfffffffffffe3fffULL,
0xfffffffffffe7fffULL,
0xfffffffffffeffffULL
},
{
0xfffffffffffc0000ULL,
0xfffffffffffc0001ULL,
0xfffffffffffc0003ULL,
0xfffffffffffc0007ULL,
0xfffffffffffc000fULL,
0xfffffffffffc001fULL,
0xfffffffffffc003fULL,
0xfffffffffffc007fULL,
0xfffffffffffc00ffULL,
0xfffffffffffc01ffULL,
0xfffffffffffc03ffULL,
0xfffffffffffc07ffULL,
0xfffffffffffc0fffULL,
0xfffffffffffc1fffULL,
0xfffffffffffc3fffULL,
0xfffffffffffc7fffULL,
0xfffffffffffcffffULL,
0xfffffffffffdffffULL
},
{
0xfffffffffff80000ULL,
0xfffffffffff80001ULL,
0xfffffffffff80003ULL,
0xfffffffffff80007ULL,
0xfffffffffff8000fULL,
0xfffffffffff8001fULL,
0xfffffffffff8003fULL,
0xfffffffffff8007fULL,
0xfffffffffff800ffULL,
0xfffffffffff801ffULL,
0xfffffffffff803ffULL,
0xfffffffffff807ffULL,
0xfffffffffff80fffULL,
0xfffffffffff81fffULL,
0xfffffffffff83fffULL,
0xfffffffffff87fffULL,
0xfffffffffff8ffffULL,
0xfffffffffff9ffffULL,
0xfffffffffffbffffULL
},
{
0xfffffffffff00000ULL,
0xfffffffffff00001ULL,
0xfffffffffff00003ULL,
0xfffffffffff00007ULL,
0xfffffffffff0000fULL,
0xfffffffffff0001fULL,
0xfffffffffff0003fULL,
0xfffffffffff0007fULL,
0xfffffffffff000ffULL,
0xfffffffffff001ffULL,
0xfffffffffff003ffULL,
0xfffffffffff007ffULL,
0xfffffffffff00fffULL,
0xfffffffffff01fffULL,
0xfffffffffff03fffULL,
0xfffffffffff07fffULL,
0xfffffffffff0ffffULL,
0xfffffffffff1ffffULL,
0xfffffffffff3ffffULL,
0xfffffffffff7ffffULL
},
{
0xffffffffffe00000ULL,
0xffffffffffe00001ULL,
0xffffffffffe00003ULL,
0xffffffffffe00007ULL,
0xffffffffffe0000fULL,
0xffffffffffe0001fULL,
0xffffffffffe0003fULL,
0xffffffffffe0007fULL,
0xffffffffffe000ffULL,
0xffffffffffe001ffULL,
0xffffffffffe003ffULL,
0xffffffffffe007ffULL,
0xffffffffffe00fffULL,
0xffffffffffe01fffULL,
0xffffffffffe03fffULL,
0xffffffffffe07fffULL,
0xffffffffffe0ffffULL,
0xffffffffffe1ffffULL,
0xffffffffffe3ffffULL,
0xffffffffffe7ffffULL,
0xffffffffffefffffULL
},
{
0xffffffffffc00000ULL,
0xffffffffffc00001ULL,
0xffffffffffc00003ULL,
0xffffffffffc00007ULL,
0xffffffffffc0000fULL,
0xffffffffffc0001fULL,
0xffffffffffc0003fULL,
0xffffffffffc0007fULL,
0xffffffffffc000ffULL,
0xffffffffffc001ffULL,
0xffffffffffc003ffULL,
0xffffffffffc007ffULL,
0xffffffffffc00fffULL,
0xffffffffffc01fffULL,
0xffffffffffc03fffULL,
0xffffffffffc07fffULL,
0xffffffffffc0ffffULL,
0xffffffffffc1ffffULL,
0xffffffffffc3ffffULL,
0xffffffffffc7ffffULL,
0xffffffffffcfffffULL,
0xffffffffffdfffffULL
},
{
0xffffffffff800000ULL,
0xffffffffff800001ULL,
0xffffffffff800003ULL,
0xffffffffff800007ULL,
0xffffffffff80000fULL,
0xffffffffff80001fULL,
0xffffffffff80003fULL,
0xffffffffff80007fULL,
0xffffffffff8000ffULL,
0xffffffffff8001ffULL,
0xffffffffff8003ffULL,
0xffffffffff8007ffULL,
0xffffffffff800fffULL,
0xffffffffff801fffULL,
0xffffffffff803fffULL,
0xffffffffff807fffULL,
0xffffffffff80ffffULL,
0xffffffffff81ffffULL,
0xffffffffff83ffffULL,
0xffffffffff87ffffULL,
0xffffffffff8fffffULL,
0xffffffffff9fffffULL,
0xffffffffffbfffffULL
},
{
0xffffffffff000000ULL,
0xffffffffff000001ULL,
0xffffffffff000003ULL,
0xffffffffff000007ULL,
0xffffffffff00000fULL,
0xffffffffff00001fULL,
0xffffffffff00003fULL,
0xffffffffff00007fULL,
0xffffffffff0000ffULL,
0xffffffffff0001ffULL,
0xffffffffff0003ffULL,
0xffffffffff0007ffULL,
0xffffffffff000fffULL,
0xffffffffff001fffULL,
0xffffffffff003fffULL,
0xffffffffff007fffULL,
0xffffffffff00ffffULL,
0xffffffffff01ffffULL,
0xffffffffff03ffffULL,
0xffffffffff07ffffULL,
0xffffffffff0fffffULL,
0xffffffffff1fffffULL,
0xffffffffff3fffffULL,
0xffffffffff7fffffULL
},
{
0xfffffffffe000000ULL,
0xfffffffffe000001ULL,
0xfffffffffe000003ULL,
0xfffffffffe000007ULL,
0xfffffffffe00000fULL,
0xfffffffffe00001fULL,
0xfffffffffe00003fULL,
0xfffffffffe00007fULL,
0xfffffffffe0000ffULL,
0xfffffffffe0001ffULL,
0xfffffffffe0003ffULL,
0xfffffffffe0007ffULL,
0xfffffffffe000fffULL,
0xfffffffffe001fffULL,
0xfffffffffe003fffULL,
0xfffffffffe007fffULL,
0xfffffffffe00ffffULL,
0xfffffffffe01ffffULL,
0xfffffffffe03ffffULL,
0xfffffffffe07ffffULL,
0xfffffffffe0fffffULL,
0xfffffffffe1fffffULL,
0xfffffffffe3fffffULL,
0xfffffffffe7fffffULL,
0xfffffffffeffffffULL
},
{
0xfffffffffc000000ULL,
0xfffffffffc000001ULL,
0xfffffffffc000003ULL,
0xfffffffffc000007ULL,
0xfffffffffc00000fULL,
0xfffffffffc00001fULL,
0xfffffffffc00003fULL,
0xfffffffffc00007fULL,
0xfffffffffc0000ffULL,
0xfffffffffc0001ffULL,
0xfffffffffc0003ffULL,
0xfffffffffc0007ffULL,
0xfffffffffc000fffULL,
0xfffffffffc001fffULL,
0xfffffffffc003fffULL,
0xfffffffffc007fffULL,
0xfffffffffc00ffffULL,
0xfffffffffc01ffffULL,
0xfffffffffc03ffffULL,
0xfffffffffc07ffffULL,
0xfffffffffc0fffffULL,
0xfffffffffc1fffffULL,
0xfffffffffc3fffffULL,
0xfffffffffc7fffffULL,
0xfffffffffcffffffULL,
0xfffffffffdffffffULL
},
{
0xfffffffff8000000ULL,
0xfffffffff8000001ULL,
0xfffffffff8000003ULL,
0xfffffffff8000007ULL,
0xfffffffff800000fULL,
0xfffffffff800001fULL,
0xfffffffff800003fULL,
0xfffffffff800007fULL,
0xfffffffff80000ffULL,
0xfffffffff80001ffULL,
0xfffffffff80003ffULL,
0xfffffffff80007ffULL,
0xfffffffff8000fffULL,
0xfffffffff8001fffULL,
0xfffffffff8003fffULL,
0xfffffffff8007fffULL,
0xfffffffff800ffffULL,
0xfffffffff801ffffULL,
0xfffffffff803ffffULL,
0xfffffffff807ffffULL,
0xfffffffff80fffffULL,
0xfffffffff81fffffULL,
0xfffffffff83fffffULL,
0xfffffffff87fffffULL,
0xfffffffff8ffffffULL,
0xfffffffff9ffffffULL,
0xfffffffffbffffffULL
},
{
0xfffffffff0000000ULL,
0xfffffffff0000001ULL,
0xfffffffff0000003ULL,
0xfffffffff0000007ULL,
0xfffffffff000000fULL,
0xfffffffff000001fULL,
0xfffffffff000003fULL,
0xfffffffff000007fULL,
0xfffffffff00000ffULL,
0xfffffffff00001ffULL,
0xfffffffff00003ffULL,
0xfffffffff00007ffULL,
0xfffffffff0000fffULL,
0xfffffffff0001fffULL,
0xfffffffff0003fffULL,
0xfffffffff0007fffULL,
0xfffffffff000ffffULL,
0xfffffffff001ffffULL,
0xfffffffff003ffffULL,
0xfffffffff007ffffULL,
0xfffffffff00fffffULL,
0xfffffffff01fffffULL,
0xfffffffff03fffffULL,
0xfffffffff07fffffULL,
0xfffffffff0ffffffULL,
0xfffffffff1ffffffULL,
0xfffffffff3ffffffULL,
0xfffffffff7ffffffULL
},
{
0xffffffffe0000000ULL,
0xffffffffe0000001ULL,
0xffffffffe0000003ULL,
0xffffffffe0000007ULL,
0xffffffffe000000fULL,
0xffffffffe000001fULL,
0xffffffffe000003fULL,
0xffffffffe000007fULL,
0xffffffffe00000ffULL,
0xffffffffe00001ffULL,
0xffffffffe00003ffULL,
0xffffffffe00007ffULL,
0xffffffffe0000fffULL,
0xffffffffe0001fffULL,
0xffffffffe0003fffULL,
0xffffffffe0007fffULL,
0xffffffffe000ffffULL,
0xffffffffe001ffffULL,
0xffffffffe003ffffULL,
0xffffffffe007ffffULL,
0xffffffffe00fffffULL,
0xffffffffe01fffffULL,
0xffffffffe03fffffULL,
0xffffffffe07fffffULL,
0xffffffffe0ffffffULL,
0xffffffffe1ffffffULL,
0xffffffffe3ffffffULL,
0xffffffffe7ffffffULL,
0xffffffffefffffffULL
},
{
0xffffffffc0000000ULL,
0xffffffffc0000001ULL,
0xffffffffc0000003ULL,
0xffffffffc0000007ULL,
0xffffffffc000000fULL,
0xffffffffc000001fULL,
0xffffffffc000003fULL,
0xffffffffc000007fULL,
0xffffffffc00000ffULL,
0xffffffffc00001ffULL,
0xffffffffc00003ffULL,
0xffffffffc00007ffULL,
0xffffffffc0000fffULL,
0xffffffffc0001fffULL,
0xffffffffc0003fffULL,
0xffffffffc0007fffULL,
0xffffffffc000ffffULL,
0xffffffffc001ffffULL,
0xffffffffc003ffffULL,
0xffffffffc007ffffULL,
0xffffffffc00fffffULL,
0xffffffffc01fffffULL,
0xffffffffc03fffffULL,
0xffffffffc07fffffULL,
0xffffffffc0ffffffULL,
0xffffffffc1ffffffULL,
0xffffffffc3ffffffULL,
0xffffffffc7ffffffULL,
0xffffffffcfffffffULL,
0xffffffffdfffffffULL
},
{
0xffffffff80000000ULL,
0xffffffff80000001ULL,
0xffffffff80000003ULL,
0xffffffff80000007ULL,
0xffffffff8000000fULL,
0xffffffff8000001fULL,
0xffffffff8000003fULL,
0xffffffff8000007fULL,
0xffffffff800000ffULL,
0xffffffff800001ffULL,
0xffffffff800003ffULL,
0xffffffff800007ffULL,
0xffffffff80000fffULL,
0xffffffff80001fffULL,
0xffffffff80003fffULL,
0xffffffff80007fffULL,
0xffffffff8000ffffULL,
0xffffffff8001ffffULL,
0xffffffff8003ffffULL,
0xffffffff8007ffffULL,
0xffffffff800fffffULL,
0xffffffff801fffffULL,
0xffffffff803fffffULL,
0xffffffff807fffffULL,
0xffffffff80ffffffULL,
0xffffffff81ffffffULL,
0xffffffff83ffffffULL,
0xffffffff87ffffffULL,
0xffffffff8fffffffULL,
0xffffffff9fffffffULL,
0xffffffffbfffffffULL
},
{
0xffffffff00000000ULL,
0xffffffff00000001ULL,
0xffffffff00000003ULL,
0xffffffff00000007ULL,
0xffffffff0000000fULL,
0xffffffff0000001fULL,
0xffffffff0000003fULL,
0xffffffff0000007fULL,
0xffffffff000000ffULL,
0xffffffff000001ffULL,
0xffffffff000003ffULL,
0xffffffff000007ffULL,
0xffffffff00000fffULL,
0xffffffff00001fffULL,
0xffffffff00003fffULL,
0xffffffff00007fffULL,
0xffffffff0000ffffULL,
0xffffffff0001ffffULL,
0xffffffff0003ffffULL,
0xffffffff0007ffffULL,
0xffffffff000fffffULL,
0xffffffff001fffffULL,
0xffffffff003fffffULL,
0xffffffff007fffffULL,
0xffffffff00ffffffULL,
0xffffffff01ffffffULL,
0xffffffff03ffffffULL,
0xffffffff07ffffffULL,
0xffffffff0fffffffULL,
0xffffffff1fffffffULL,
0xffffffff3fffffffULL,
0xffffffff7fffffffULL
},
{
0xfffffffe00000000ULL,
0xfffffffe00000001ULL,
0xfffffffe00000003ULL,
0xfffffffe00000007ULL,
0xfffffffe0000000fULL,
0xfffffffe0000001fULL,
0xfffffffe0000003fULL,
0xfffffffe0000007fULL,
0xfffffffe000000ffULL,
0xfffffffe000001ffULL,
0xfffffffe000003ffULL,
0xfffffffe000007ffULL,
0xfffffffe00000fffULL,
0xfffffffe00001fffULL,
0xfffffffe00003fffULL,
0xfffffffe00007fffULL,
0xfffffffe0000ffffULL,
0xfffffffe0001ffffULL,
0xfffffffe0003ffffULL,
0xfffffffe0007ffffULL,
0xfffffffe000fffffULL,
0xfffffffe001fffffULL,
0xfffffffe003fffffULL,
0xfffffffe007fffffULL,
0xfffffffe00ffffffULL,
0xfffffffe01ffffffULL,
0xfffffffe03ffffffULL,
0xfffffffe07ffffffULL,
0xfffffffe0fffffffULL,
0xfffffffe1fffffffULL,
0xfffffffe3fffffffULL,
0xfffffffe7fffffffULL,
0xfffffffeffffffffULL
},
{
0xfffffffc00000000ULL,
0xfffffffc00000001ULL,
0xfffffffc00000003ULL,
0xfffffffc00000007ULL,
0xfffffffc0000000fULL,
0xfffffffc0000001fULL,
0xfffffffc0000003fULL,
0xfffffffc0000007fULL,
0xfffffffc000000ffULL,
0xfffffffc000001ffULL,
0xfffffffc000003ffULL,
0xfffffffc000007ffULL,
0xfffffffc00000fffULL,
0xfffffffc00001fffULL,
0xfffffffc00003fffULL,
0xfffffffc00007fffULL,
0xfffffffc0000ffffULL,
0xfffffffc0001ffffULL,
0xfffffffc0003ffffULL,
0xfffffffc0007ffffULL,
0xfffffffc000fffffULL,
0xfffffffc001fffffULL,
0xfffffffc003fffffULL,
0xfffffffc007fffffULL,
0xfffffffc00ffffffULL,
0xfffffffc01ffffffULL,
0xfffffffc03ffffffULL,
0xfffffffc07ffffffULL,
0xfffffffc0fffffffULL,
0xfffffffc1fffffffULL,
0xfffffffc3fffffffULL,
0xfffffffc7fffffffULL,
0xfffffffcffffffffULL,
0xfffffffdffffffffULL
},
{
0xfffffff800000000ULL,
0xfffffff800000001ULL,
0xfffffff800000003ULL,
0xfffffff800000007ULL,
0xfffffff80000000fULL,
0xfffffff80000001fULL,
0xfffffff80000003fULL,
0xfffffff80000007fULL,
0xfffffff8000000ffULL,
0xfffffff8000001ffULL,
0xfffffff8000003ffULL,
0xfffffff8000007ffULL,
0xfffffff800000fffULL,
0xfffffff800001fffULL,
0xfffffff800003fffULL,
0xfffffff800007fffULL,
0xfffffff80000ffffULL,
0xfffffff80001ffffULL,
0xfffffff80003ffffULL,
0xfffffff80007ffffULL,
0xfffffff8000fffffULL,
0xfffffff8001fffffULL,
0xfffffff8003fffffULL,
0xfffffff8007fffffULL,
0xfffffff800ffffffULL,
0xfffffff801ffffffULL,
0xfffffff803ffffffULL,
0xfffffff807ffffffULL,
0xfffffff80fffffffULL,
0xfffffff81fffffffULL,
0xfffffff83fffffffULL,
0xfffffff87fffffffULL,
0xfffffff8ffffffffULL,
0xfffffff9ffffffffULL,
0xfffffffbffffffffULL
},
{
0xfffffff000000000ULL,
0xfffffff000000001ULL,
0xfffffff000000003ULL,
0xfffffff000000007ULL,
0xfffffff00000000fULL,
0xfffffff00000001fULL,
0xfffffff00000003fULL,
0xfffffff00000007fULL,
0xfffffff0000000ffULL,
0xfffffff0000001ffULL,
0xfffffff0000003ffULL,
0xfffffff0000007ffULL,
0xfffffff000000fffULL,
0xfffffff000001fffULL,
0xfffffff000003fffULL,
0xfffffff000007fffULL,
0xfffffff00000ffffULL,
0xfffffff00001ffffULL,
0xfffffff00003ffffULL,
0xfffffff00007ffffULL,
0xfffffff0000fffffULL,
0xfffffff0001fffffULL,
0xfffffff0003fffffULL,
0xfffffff0007fffffULL,
0xfffffff000ffffffULL,
0xfffffff001ffffffULL,
0xfffffff003ffffffULL,
0xfffffff007ffffffULL,
0xfffffff00fffffffULL,
0xfffffff01fffffffULL,
0xfffffff03fffffffULL,
0xfffffff07fffffffULL,
0xfffffff0ffffffffULL,
0xfffffff1ffffffffULL,
0xfffffff3ffffffffULL,
0xfffffff7ffffffffULL
},
{
0xffffffe000000000ULL,
0xffffffe000000001ULL,
0xffffffe000000003ULL,
0xffffffe000000007ULL,
0xffffffe00000000fULL,
0xffffffe00000001fULL,
0xffffffe00000003fULL,
0xffffffe00000007fULL,
0xffffffe0000000ffULL,
0xffffffe0000001ffULL,
0xffffffe0000003ffULL,
0xffffffe0000007ffULL,
0xffffffe000000fffULL,
0xffffffe000001fffULL,
0xffffffe000003fffULL,
0xffffffe000007fffULL,
0xffffffe00000ffffULL,
0xffffffe00001ffffULL,
0xffffffe00003ffffULL,
0xffffffe00007ffffULL,
0xffffffe0000fffffULL,
0xffffffe0001fffffULL,
0xffffffe0003fffffULL,
0xffffffe0007fffffULL,
0xffffffe000ffffffULL,
0xffffffe001ffffffULL,
0xffffffe003ffffffULL,
0xffffffe007ffffffULL,
0xffffffe00fffffffULL,
0xffffffe01fffffffULL,
0xffffffe03fffffffULL,
0xffffffe07fffffffULL,
0xffffffe0ffffffffULL,
0xffffffe1ffffffffULL,
0xffffffe3ffffffffULL,
0xffffffe7ffffffffULL,
0xffffffefffffffffULL
},
{
0xffffffc000000000ULL,
0xffffffc000000001ULL,
0xffffffc000000003ULL,
0xffffffc000000007ULL,
0xffffffc00000000fULL,
0xffffffc00000001fULL,
0xffffffc00000003fULL,
0xffffffc00000007fULL,
0xffffffc0000000ffULL,
0xffffffc0000001ffULL,
0xffffffc0000003ffULL,
0xffffffc0000007ffULL,
0xffffffc000000fffULL,
0xffffffc000001fffULL,
0xffffffc000003fffULL,
0xffffffc000007fffULL,
0xffffffc00000ffffULL,
0xffffffc00001ffffULL,
0xffffffc00003ffffULL,
0xffffffc00007ffffULL,
0xffffffc0000fffffULL,
0xffffffc0001fffffULL,
0xffffffc0003fffffULL,
0xffffffc0007fffffULL,
0xffffffc000ffffffULL,
0xffffffc001ffffffULL,
0xffffffc003ffffffULL,
0xffffffc007ffffffULL,
0xffffffc00fffffffULL,
0xffffffc01fffffffULL,
0xffffffc03fffffffULL,
0xffffffc07fffffffULL,
0xffffffc0ffffffffULL,
0xffffffc1ffffffffULL,
0xffffffc3ffffffffULL,
0xffffffc7ffffffffULL,
0xffffffcfffffffffULL,
0xffffffdfffffffffULL
},
{
0xffffff8000000000ULL,
0xffffff8000000001ULL,
0xffffff8000000003ULL,
0xffffff8000000007ULL,
0xffffff800000000fULL,
0xffffff800000001fULL,
0xffffff800000003fULL,
0xffffff800000007fULL,
0xffffff80000000ffULL,
0xffffff80000001ffULL,
0xffffff80000003ffULL,
0xffffff80000007ffULL,
0xffffff8000000fffULL,
0xffffff8000001fffULL,
0xffffff8000003fffULL,
0xffffff8000007fffULL,
0xffffff800000ffffULL,
0xffffff800001ffffULL,
0xffffff800003ffffULL,
0xffffff800007ffffULL,
0xffffff80000fffffULL,
0xffffff80001fffffULL,
0xffffff80003fffffULL,
0xffffff80007fffffULL,
0xffffff8000ffffffULL,
0xffffff8001ffffffULL,
0xffffff8003ffffffULL,
0xffffff8007ffffffULL,
0xffffff800fffffffULL,
0xffffff801fffffffULL,
0xffffff803fffffffULL,
0xffffff807fffffffULL,
0xffffff80ffffffffULL,
0xffffff81ffffffffULL,
0xffffff83ffffffffULL,
0xffffff87ffffffffULL,
0xffffff8fffffffffULL,
0xffffff9fffffffffULL,
0xffffffbfffffffffULL
},
{
0xffffff0000000000ULL,
0xffffff0000000001ULL,
0xffffff0000000003ULL,
0xffffff0000000007ULL,
0xffffff000000000fULL,
0xffffff000000001fULL,
0xffffff000000003fULL,
0xffffff000000007fULL,
0xffffff00000000ffULL,
0xffffff00000001ffULL,
0xffffff00000003ffULL,
0xffffff00000007ffULL,
0xffffff0000000fffULL,
0xffffff0000001fffULL,
0xffffff0000003fffULL,
0xffffff0000007fffULL,
0xffffff000000ffffULL,
0xffffff000001ffffULL,
0xffffff000003ffffULL,
0xffffff000007ffffULL,
0xffffff00000fffffULL,
0xffffff00001fffffULL,
0xffffff00003fffffULL,
0xffffff00007fffffULL,
0xffffff0000ffffffULL,
0xffffff0001ffffffULL,
0xffffff0003ffffffULL,
0xffffff0007ffffffULL,
0xffffff000fffffffULL,
0xffffff001fffffffULL,
0xffffff003fffffffULL,
0xffffff007fffffffULL,
0xffffff00ffffffffULL,
0xffffff01ffffffffULL,
0xffffff03ffffffffULL,
0xffffff07ffffffffULL,
0xffffff0fffffffffULL,
0xffffff1fffffffffULL,
0xffffff3fffffffffULL,
0xffffff7fffffffffULL
},
{
0xfffffe0000000000ULL,
0xfffffe0000000001ULL,
0xfffffe0000000003ULL,
0xfffffe0000000007ULL,
0xfffffe000000000fULL,
0xfffffe000000001fULL,
0xfffffe000000003fULL,
0xfffffe000000007fULL,
0xfffffe00000000ffULL,
0xfffffe00000001ffULL,
0xfffffe00000003ffULL,
0xfffffe00000007ffULL,
0xfffffe0000000fffULL,
0xfffffe0000001fffULL,
0xfffffe0000003fffULL,
0xfffffe0000007fffULL,
0xfffffe000000ffffULL,
0xfffffe000001ffffULL,
0xfffffe000003ffffULL,
0xfffffe000007ffffULL,
0xfffffe00000fffffULL,
0xfffffe00001fffffULL,
0xfffffe00003fffffULL,
0xfffffe00007fffffULL,
0xfffffe0000ffffffULL,
0xfffffe0001ffffffULL,
0xfffffe0003ffffffULL,
0xfffffe0007ffffffULL,
0xfffffe000fffffffULL,
0xfffffe001fffffffULL,
0xfffffe003fffffffULL,
0xfffffe007fffffffULL,
0xfffffe00ffffffffULL,
0xfffffe01ffffffffULL,
0xfffffe03ffffffffULL,
0xfffffe07ffffffffULL,
0xfffffe0fffffffffULL,
0xfffffe1fffffffffULL,
0xfffffe3fffffffffULL,
0xfffffe7fffffffffULL,
0xfffffeffffffffffULL
},
{
0xfffffc0000000000ULL,
0xfffffc0000000001ULL,
0xfffffc0000000003ULL,
0xfffffc0000000007ULL,
0xfffffc000000000fULL,
0xfffffc000000001fULL,
0xfffffc000000003fULL,
0xfffffc000000007fULL,
0xfffffc00000000ffULL,
0xfffffc00000001ffULL,
0xfffffc00000003ffULL,
0xfffffc00000007ffULL,
0xfffffc0000000fffULL,
0xfffffc0000001fffULL,
0xfffffc0000003fffULL,
0xfffffc0000007fffULL,
0xfffffc000000ffffULL,
0xfffffc000001ffffULL,
0xfffffc000003ffffULL,
0xfffffc000007ffffULL,
0xfffffc00000fffffULL,
0xfffffc00001fffffULL,
0xfffffc00003fffffULL,
0xfffffc00007fffffULL,
0xfffffc0000ffffffULL,
0xfffffc0001ffffffULL,
0xfffffc0003ffffffULL,
0xfffffc0007ffffffULL,
0xfffffc000fffffffULL,
0xfffffc001fffffffULL,
0xfffffc003fffffffULL,
0xfffffc007fffffffULL,
0xfffffc00ffffffffULL,
0xfffffc01ffffffffULL,
0xfffffc03ffffffffULL,
0xfffffc07ffffffffULL,
0xfffffc0fffffffffULL,
0xfffffc1fffffffffULL,
0xfffffc3fffffffffULL,
0xfffffc7fffffffffULL,
0xfffffcffffffffffULL,
0xfffffdffffffffffULL
},
{
0xfffff80000000000ULL,
0xfffff80000000001ULL,
0xfffff80000000003ULL,
0xfffff80000000007ULL,
0xfffff8000000000fULL,
0xfffff8000000001fULL,
0xfffff8000000003fULL,
0xfffff8000000007fULL,
0xfffff800000000ffULL,
0xfffff800000001ffULL,
0xfffff800000003ffULL,
0xfffff800000007ffULL,
0xfffff80000000fffULL,
0xfffff80000001fffULL,
0xfffff80000003fffULL,
0xfffff80000007fffULL,
0xfffff8000000ffffULL,
0xfffff8000001ffffULL,
0xfffff8000003ffffULL,
0xfffff8000007ffffULL,
0xfffff800000fffffULL,
0xfffff800001fffffULL,
0xfffff800003fffffULL,
0xfffff800007fffffULL,
0xfffff80000ffffffULL,
0xfffff80001ffffffULL,
0xfffff80003ffffffULL,
0xfffff80007ffffffULL,
0xfffff8000fffffffULL,
0xfffff8001fffffffULL,
0xfffff8003fffffffULL,
0xfffff8007fffffffULL,
0xfffff800ffffffffULL,
0xfffff801ffffffffULL,
0xfffff803ffffffffULL,
0xfffff807ffffffffULL,
0xfffff80fffffffffULL,
0xfffff81fffffffffULL,
0xfffff83fffffffffULL,
0xfffff87fffffffffULL,
0xfffff8ffffffffffULL,
0xfffff9ffffffffffULL,
0xfffffbffffffffffULL
},
{
0xfffff00000000000ULL,
0xfffff00000000001ULL,
0xfffff00000000003ULL,
0xfffff00000000007ULL,
0xfffff0000000000fULL,
0xfffff0000000001fULL,
0xfffff0000000003fULL,
0xfffff0000000007fULL,
0xfffff000000000ffULL,
0xfffff000000001ffULL,
0xfffff000000003ffULL,
0xfffff000000007ffULL,
0xfffff00000000fffULL,
0xfffff00000001fffULL,
0xfffff00000003fffULL,
0xfffff00000007fffULL,
0xfffff0000000ffffULL,
0xfffff0000001ffffULL,
0xfffff0000003ffffULL,
0xfffff0000007ffffULL,
0xfffff000000fffffULL,
0xfffff000001fffffULL,
0xfffff000003fffffULL,
0xfffff000007fffffULL,
0xfffff00000ffffffULL,
0xfffff00001ffffffULL,
0xfffff00003ffffffULL,
0xfffff00007ffffffULL,
0xfffff0000fffffffULL,
0xfffff0001fffffffULL,
0xfffff0003fffffffULL,
0xfffff0007fffffffULL,
0xfffff000ffffffffULL,
0xfffff001ffffffffULL,
0xfffff003ffffffffULL,
0xfffff007ffffffffULL,
0xfffff00fffffffffULL,
0xfffff01fffffffffULL,
0xfffff03fffffffffULL,
0xfffff07fffffffffULL,
0xfffff0ffffffffffULL,
0xfffff1ffffffffffULL,
0xfffff3ffffffffffULL,
0xfffff7ffffffffffULL
},
{
0xffffe00000000000ULL,
0xffffe00000000001ULL,
0xffffe00000000003ULL,
0xffffe00000000007ULL,
0xffffe0000000000fULL,
0xffffe0000000001fULL,
0xffffe0000000003fULL,
0xffffe0000000007fULL,
0xffffe000000000ffULL,
0xffffe000000001ffULL,
0xffffe000000003ffULL,
0xffffe000000007ffULL,
0xffffe00000000fffULL,
0xffffe00000001fffULL,
0xffffe00000003fffULL,
0xffffe00000007fffULL,
0xffffe0000000ffffULL,
0xffffe0000001ffffULL,
0xffffe0000003ffffULL,
0xffffe0000007ffffULL,
0xffffe000000fffffULL,
0xffffe000001fffffULL,
0xffffe000003fffffULL,
0xffffe000007fffffULL,
0xffffe00000ffffffULL,
0xffffe00001ffffffULL,
0xffffe00003ffffffULL,
0xffffe00007ffffffULL,
0xffffe0000fffffffULL,
0xffffe0001fffffffULL,
0xffffe0003fffffffULL,
0xffffe0007fffffffULL,
0xffffe000ffffffffULL,
0xffffe001ffffffffULL,
0xffffe003ffffffffULL,
0xffffe007ffffffffULL,
0xffffe00fffffffffULL,
0xffffe01fffffffffULL,
0xffffe03fffffffffULL,
0xffffe07fffffffffULL,
0xffffe0ffffffffffULL,
0xffffe1ffffffffffULL,
0xffffe3ffffffffffULL,
0xffffe7ffffffffffULL,
0xffffefffffffffffULL
},
{
0xffffc00000000000ULL,
0xffffc00000000001ULL,
0xffffc00000000003ULL,
0xffffc00000000007ULL,
0xffffc0000000000fULL,
0xffffc0000000001fULL,
0xffffc0000000003fULL,
0xffffc0000000007fULL,
0xffffc000000000ffULL,
0xffffc000000001ffULL,
0xffffc000000003ffULL,
0xffffc000000007ffULL,
0xffffc00000000fffULL,
0xffffc00000001fffULL,
0xffffc00000003fffULL,
0xffffc00000007fffULL,
0xffffc0000000ffffULL,
0xffffc0000001ffffULL,
0xffffc0000003ffffULL,
0xffffc0000007ffffULL,
0xffffc000000fffffULL,
0xffffc000001fffffULL,
0xffffc000003fffffULL,
0xffffc000007fffffULL,
0xffffc00000ffffffULL,
0xffffc00001ffffffULL,
0xffffc00003ffffffULL,
0xffffc00007ffffffULL,
0xffffc0000fffffffULL,
0xffffc0001fffffffULL,
0xffffc0003fffffffULL,
0xffffc0007fffffffULL,
0xffffc000ffffffffULL,
0xffffc001ffffffffULL,
0xffffc003ffffffffULL,
0xffffc007ffffffffULL,
0xffffc00fffffffffULL,
0xffffc01fffffffffULL,
0xffffc03fffffffffULL,
0xffffc07fffffffffULL,
0xffffc0ffffffffffULL,
0xffffc1ffffffffffULL,
0xffffc3ffffffffffULL,
0xffffc7ffffffffffULL,
0xffffcfffffffffffULL,
0xffffdfffffffffffULL
},
{
0xffff800000000000ULL,
0xffff800000000001ULL,
0xffff800000000003ULL,
0xffff800000000007ULL,
0xffff80000000000fULL,
0xffff80000000001fULL,
0xffff80000000003fULL,
0xffff80000000007fULL,
0xffff8000000000ffULL,
0xffff8000000001ffULL,
0xffff8000000003ffULL,
0xffff8000000007ffULL,
0xffff800000000fffULL,
0xffff800000001fffULL,
0xffff800000003fffULL,
0xffff800000007fffULL,
0xffff80000000ffffULL,
0xffff80000001ffffULL,
0xffff80000003ffffULL,
0xffff80000007ffffULL,
0xffff8000000fffffULL,
0xffff8000001fffffULL,
0xffff8000003fffffULL,
0xffff8000007fffffULL,
0xffff800000ffffffULL,
0xffff800001ffffffULL,
0xffff800003ffffffULL,
0xffff800007ffffffULL,
0xffff80000fffffffULL,
0xffff80001fffffffULL,
0xffff80003fffffffULL,
0xffff80007fffffffULL,
0xffff8000ffffffffULL,
0xffff8001ffffffffULL,
0xffff8003ffffffffULL,
0xffff8007ffffffffULL,
0xffff800fffffffffULL,
0xffff801fffffffffULL,
0xffff803fffffffffULL,
0xffff807fffffffffULL,
0xffff80ffffffffffULL,
0xffff81ffffffffffULL,
0xffff83ffffffffffULL,
0xffff87ffffffffffULL,
0xffff8fffffffffffULL,
0xffff9fffffffffffULL,
0xffffbfffffffffffULL
},
{
0xffff000000000000ULL,
0xffff000000000001ULL,
0xffff000000000003ULL,
0xffff000000000007ULL,
0xffff00000000000fULL,
0xffff00000000001fULL,
0xffff00000000003fULL,
0xffff00000000007fULL,
0xffff0000000000ffULL,
0xffff0000000001ffULL,
0xffff0000000003ffULL,
0xffff0000000007ffULL,
0xffff000000000fffULL,
0xffff000000001fffULL,
0xffff000000003fffULL,
0xffff000000007fffULL,
0xffff00000000ffffULL,
0xffff00000001ffffULL,
0xffff00000003ffffULL,
0xffff00000007ffffULL,
0xffff0000000fffffULL,
0xffff0000001fffffULL,
0xffff0000003fffffULL,
0xffff0000007fffffULL,
0xffff000000ffffffULL,
0xffff000001ffffffULL,
0xffff000003ffffffULL,
0xffff000007ffffffULL,
0xffff00000fffffffULL,
0xffff00001fffffffULL,
0xffff00003fffffffULL,
0xffff00007fffffffULL,
0xffff0000ffffffffULL,
0xffff0001ffffffffULL,
0xffff0003ffffffffULL,
0xffff0007ffffffffULL,
0xffff000fffffffffULL,
0xffff001fffffffffULL,
0xffff003fffffffffULL,
0xffff007fffffffffULL,
0xffff00ffffffffffULL,
0xffff01ffffffffffULL,
0xffff03ffffffffffULL,
0xffff07ffffffffffULL,
0xffff0fffffffffffULL,
0xffff1fffffffffffULL,
0xffff3fffffffffffULL,
0xffff7fffffffffffULL
},
{
0xfffe000000000000ULL,
0xfffe000000000001ULL,
0xfffe000000000003ULL,
0xfffe000000000007ULL,
0xfffe00000000000fULL,
0xfffe00000000001fULL,
0xfffe00000000003fULL,
0xfffe00000000007fULL,
0xfffe0000000000ffULL,
0xfffe0000000001ffULL,
0xfffe0000000003ffULL,
0xfffe0000000007ffULL,
0xfffe000000000fffULL,
0xfffe000000001fffULL,
0xfffe000000003fffULL,
0xfffe000000007fffULL,
0xfffe00000000ffffULL,
0xfffe00000001ffffULL,
0xfffe00000003ffffULL,
0xfffe00000007ffffULL,
0xfffe0000000fffffULL,
0xfffe0000001fffffULL,
0xfffe0000003fffffULL,
0xfffe0000007fffffULL,
0xfffe000000ffffffULL,
0xfffe000001ffffffULL,
0xfffe000003ffffffULL,
0xfffe000007ffffffULL,
0xfffe00000fffffffULL,
0xfffe00001fffffffULL,
0xfffe00003fffffffULL,
0xfffe00007fffffffULL,
0xfffe0000ffffffffULL,
0xfffe0001ffffffffULL,
0xfffe0003ffffffffULL,
0xfffe0007ffffffffULL,
0xfffe000fffffffffULL,
0xfffe001fffffffffULL,
0xfffe003fffffffffULL,
0xfffe007fffffffffULL,
0xfffe00ffffffffffULL,
0xfffe01ffffffffffULL,
0xfffe03ffffffffffULL,
0xfffe07ffffffffffULL,
0xfffe0fffffffffffULL,
0xfffe1fffffffffffULL,
0xfffe3fffffffffffULL,
0xfffe7fffffffffffULL,
0xfffeffffffffffffULL
},
{
0xfffc000000000000ULL,
0xfffc000000000001ULL,
0xfffc000000000003ULL,
0xfffc000000000007ULL,
0xfffc00000000000fULL,
0xfffc00000000001fULL,
0xfffc00000000003fULL,
0xfffc00000000007fULL,
0xfffc0000000000ffULL,
0xfffc0000000001ffULL,
0xfffc0000000003ffULL,
0xfffc0000000007ffULL,
0xfffc000000000fffULL,
0xfffc000000001fffULL,
0xfffc000000003fffULL,
0xfffc000000007fffULL,
0xfffc00000000ffffULL,
0xfffc00000001ffffULL,
0xfffc00000003ffffULL,
0xfffc00000007ffffULL,
0xfffc0000000fffffULL,
0xfffc0000001fffffULL,
0xfffc0000003fffffULL,
0xfffc0000007fffffULL,
0xfffc000000ffffffULL,
0xfffc000001ffffffULL,
0xfffc000003ffffffULL,
0xfffc000007ffffffULL,
0xfffc00000fffffffULL,
0xfffc00001fffffffULL,
0xfffc00003fffffffULL,
0xfffc00007fffffffULL,
0xfffc0000ffffffffULL,
0xfffc0001ffffffffULL,
0xfffc0003ffffffffULL,
0xfffc0007ffffffffULL,
0xfffc000fffffffffULL,
0xfffc001fffffffffULL,
0xfffc003fffffffffULL,
0xfffc007fffffffffULL,
0xfffc00ffffffffffULL,
0xfffc01ffffffffffULL,
0xfffc03ffffffffffULL,
0xfffc07ffffffffffULL,
0xfffc0fffffffffffULL,
0xfffc1fffffffffffULL,
0xfffc3fffffffffffULL,
0xfffc7fffffffffffULL,
0xfffcffffffffffffULL,
0xfffdffffffffffffULL
},
{
0xfff8000000000000ULL,
0xfff8000000000001ULL,
0xfff8000000000003ULL,
0xfff8000000000007ULL,
0xfff800000000000fULL,
0xfff800000000001fULL,
0xfff800000000003fULL,
0xfff800000000007fULL,
0xfff80000000000ffULL,
0xfff80000000001ffULL,
0xfff80000000003ffULL,
0xfff80000000007ffULL,
0xfff8000000000fffULL,
0xfff8000000001fffULL,
0xfff8000000003fffULL,
0xfff8000000007fffULL,
0xfff800000000ffffULL,
0xfff800000001ffffULL,
0xfff800000003ffffULL,
0xfff800000007ffffULL,
0xfff80000000fffffULL,
0xfff80000001fffffULL,
0xfff80000003fffffULL,
0xfff80000007fffffULL,
0xfff8000000ffffffULL,
0xfff8000001ffffffULL,
0xfff8000003ffffffULL,
0xfff8000007ffffffULL,
0xfff800000fffffffULL,
0xfff800001fffffffULL,
0xfff800003fffffffULL,
0xfff800007fffffffULL,
0xfff80000ffffffffULL,
0xfff80001ffffffffULL,
0xfff80003ffffffffULL,
0xfff80007ffffffffULL,
0xfff8000fffffffffULL,
0xfff8001fffffffffULL,
0xfff8003fffffffffULL,
0xfff8007fffffffffULL,
0xfff800ffffffffffULL,
0xfff801ffffffffffULL,
0xfff803ffffffffffULL,
0xfff807ffffffffffULL,
0xfff80fffffffffffULL,
0xfff81fffffffffffULL,
0xfff83fffffffffffULL,
0xfff87fffffffffffULL,
0xfff8ffffffffffffULL,
0xfff9ffffffffffffULL,
0xfffbffffffffffffULL
},
{
0xfff0000000000000ULL,
0xfff0000000000001ULL,
0xfff0000000000003ULL,
0xfff0000000000007ULL,
0xfff000000000000fULL,
0xfff000000000001fULL,
0xfff000000000003fULL,
0xfff000000000007fULL,
0xfff00000000000ffULL,
0xfff00000000001ffULL,
0xfff00000000003ffULL,
0xfff00000000007ffULL,
0xfff0000000000fffULL,
0xfff0000000001fffULL,
0xfff0000000003fffULL,
0xfff0000000007fffULL,
0xfff000000000ffffULL,
0xfff000000001ffffULL,
0xfff000000003ffffULL,
0xfff000000007ffffULL,
0xfff00000000fffffULL,
0xfff00000001fffffULL,
0xfff00000003fffffULL,
0xfff00000007fffffULL,
0xfff0000000ffffffULL,
0xfff0000001ffffffULL,
0xfff0000003ffffffULL,
0xfff0000007ffffffULL,
0xfff000000fffffffULL,
0xfff000001fffffffULL,
0xfff000003fffffffULL,
0xfff000007fffffffULL,
0xfff00000ffffffffULL,
0xfff00001ffffffffULL,
0xfff00003ffffffffULL,
0xfff00007ffffffffULL,
0xfff0000fffffffffULL,
0xfff0001fffffffffULL,
0xfff0003fffffffffULL,
0xfff0007fffffffffULL,
0xfff000ffffffffffULL,
0xfff001ffffffffffULL,
0xfff003ffffffffffULL,
0xfff007ffffffffffULL,
0xfff00fffffffffffULL,
0xfff01fffffffffffULL,
0xfff03fffffffffffULL,
0xfff07fffffffffffULL,
0xfff0ffffffffffffULL,
0xfff1ffffffffffffULL,
0xfff3ffffffffffffULL,
0xfff7ffffffffffffULL
},
{
0xffe0000000000000ULL,
0xffe0000000000001ULL,
0xffe0000000000003ULL,
0xffe0000000000007ULL,
0xffe000000000000fULL,
0xffe000000000001fULL,
0xffe000000000003fULL,
0xffe000000000007fULL,
0xffe00000000000ffULL,
0xffe00000000001ffULL,
0xffe00000000003ffULL,
0xffe00000000007ffULL,
0xffe0000000000fffULL,
0xffe0000000001fffULL,
0xffe0000000003fffULL,
0xffe0000000007fffULL,
0xffe000000000ffffULL,
0xffe000000001ffffULL,
0xffe000000003ffffULL,
0xffe000000007ffffULL,
0xffe00000000fffffULL,
0xffe00000001fffffULL,
0xffe00000003fffffULL,
0xffe00000007fffffULL,
0xffe0000000ffffffULL,
0xffe0000001ffffffULL,
0xffe0000003ffffffULL,
0xffe0000007ffffffULL,
0xffe000000fffffffULL,
0xffe000001fffffffULL,
0xffe000003fffffffULL,
0xffe000007fffffffULL,
0xffe00000ffffffffULL,
0xffe00001ffffffffULL,
0xffe00003ffffffffULL,
0xffe00007ffffffffULL,
0xffe0000fffffffffULL,
0xffe0001fffffffffULL,
0xffe0003fffffffffULL,
0xffe0007fffffffffULL,
0xffe000ffffffffffULL,
0xffe001ffffffffffULL,
0xffe003ffffffffffULL,
0xffe007ffffffffffULL,
0xffe00fffffffffffULL,
0xffe01fffffffffffULL,
0xffe03fffffffffffULL,
0xffe07fffffffffffULL,
0xffe0ffffffffffffULL,
0xffe1ffffffffffffULL,
0xffe3ffffffffffffULL,
0xffe7ffffffffffffULL,
0xffefffffffffffffULL
},
{
0xffc0000000000000ULL,
0xffc0000000000001ULL,
0xffc0000000000003ULL,
0xffc0000000000007ULL,
0xffc000000000000fULL,
0xffc000000000001fULL,
0xffc000000000003fULL,
0xffc000000000007fULL,
0xffc00000000000ffULL,
0xffc00000000001ffULL,
0xffc00000000003ffULL,
0xffc00000000007ffULL,
0xffc0000000000fffULL,
0xffc0000000001fffULL,
0xffc0000000003fffULL,
0xffc0000000007fffULL,
0xffc000000000ffffULL,
0xffc000000001ffffULL,
0xffc000000003ffffULL,
0xffc000000007ffffULL,
0xffc00000000fffffULL,
0xffc00000001fffffULL,
0xffc00000003fffffULL,
0xffc00000007fffffULL,
0xffc0000000ffffffULL,
0xffc0000001ffffffULL,
0xffc0000003ffffffULL,
0xffc0000007ffffffULL,
0xffc000000fffffffULL,
0xffc000001fffffffULL,
0xffc000003fffffffULL,
0xffc000007fffffffULL,
0xffc00000ffffffffULL,
0xffc00001ffffffffULL,
0xffc00003ffffffffULL,
0xffc00007ffffffffULL,
0xffc0000fffffffffULL,
0xffc0001fffffffffULL,
0xffc0003fffffffffULL,
0xffc0007fffffffffULL,
0xffc000ffffffffffULL,
0xffc001ffffffffffULL,
0xffc003ffffffffffULL,
0xffc007ffffffffffULL,
0xffc00fffffffffffULL,
0xffc01fffffffffffULL,
0xffc03fffffffffffULL,
0xffc07fffffffffffULL,
0xffc0ffffffffffffULL,
0xffc1ffffffffffffULL,
0xffc3ffffffffffffULL,
0xffc7ffffffffffffULL,
0xffcfffffffffffffULL,
0xffdfffffffffffffULL
},
{
0xff80000000000000ULL,
0xff80000000000001ULL,
0xff80000000000003ULL,
0xff80000000000007ULL,
0xff8000000000000fULL,
0xff8000000000001fULL,
0xff8000000000003fULL,
0xff8000000000007fULL,
0xff800000000000ffULL,
0xff800000000001ffULL,
0xff800000000003ffULL,
0xff800000000007ffULL,
0xff80000000000fffULL,
0xff80000000001fffULL,
0xff80000000003fffULL,
0xff80000000007fffULL,
0xff8000000000ffffULL,
0xff8000000001ffffULL,
0xff8000000003ffffULL,
0xff8000000007ffffULL,
0xff800000000fffffULL,
0xff800000001fffffULL,
0xff800000003fffffULL,
0xff800000007fffffULL,
0xff80000000ffffffULL,
0xff80000001ffffffULL,
0xff80000003ffffffULL,
0xff80000007ffffffULL,
0xff8000000fffffffULL,
0xff8000001fffffffULL,
0xff8000003fffffffULL,
0xff8000007fffffffULL,
0xff800000ffffffffULL,
0xff800001ffffffffULL,
0xff800003ffffffffULL,
0xff800007ffffffffULL,
0xff80000fffffffffULL,
0xff80001fffffffffULL,
0xff80003fffffffffULL,
0xff80007fffffffffULL,
0xff8000ffffffffffULL,
0xff8001ffffffffffULL,
0xff8003ffffffffffULL,
0xff8007ffffffffffULL,
0xff800fffffffffffULL,
0xff801fffffffffffULL,
0xff803fffffffffffULL,
0xff807fffffffffffULL,
0xff80ffffffffffffULL,
0xff81ffffffffffffULL,
0xff83ffffffffffffULL,
0xff87ffffffffffffULL,
0xff8fffffffffffffULL,
0xff9fffffffffffffULL,
0xffbfffffffffffffULL
},
{
0xff00000000000000ULL,
0xff00000000000001ULL,
0xff00000000000003ULL,
0xff00000000000007ULL,
0xff0000000000000fULL,
0xff0000000000001fULL,
0xff0000000000003fULL,
0xff0000000000007fULL,
0xff000000000000ffULL,
0xff000000000001ffULL,
0xff000000000003ffULL,
0xff000000000007ffULL,
0xff00000000000fffULL,
0xff00000000001fffULL,
0xff00000000003fffULL,
0xff00000000007fffULL,
0xff0000000000ffffULL,
0xff0000000001ffffULL,
0xff0000000003ffffULL,
0xff0000000007ffffULL,
0xff000000000fffffULL,
0xff000000001fffffULL,
0xff000000003fffffULL,
0xff000000007fffffULL,
0xff00000000ffffffULL,
0xff00000001ffffffULL,
0xff00000003ffffffULL,
0xff00000007ffffffULL,
0xff0000000fffffffULL,
0xff0000001fffffffULL,
0xff0000003fffffffULL,
0xff0000007fffffffULL,
0xff000000ffffffffULL,
0xff000001ffffffffULL,
0xff000003ffffffffULL,
0xff000007ffffffffULL,
0xff00000fffffffffULL,
0xff00001fffffffffULL,
0xff00003fffffffffULL,
0xff00007fffffffffULL,
0xff0000ffffffffffULL,
0xff0001ffffffffffULL,
0xff0003ffffffffffULL,
0xff0007ffffffffffULL,
0xff000fffffffffffULL,
0xff001fffffffffffULL,
0xff003fffffffffffULL,
0xff007fffffffffffULL,
0xff00ffffffffffffULL,
0xff01ffffffffffffULL,
0xff03ffffffffffffULL,
0xff07ffffffffffffULL,
0xff0fffffffffffffULL,
0xff1fffffffffffffULL,
0xff3fffffffffffffULL,
0xff7fffffffffffffULL
},
{
0xfe00000000000000ULL,
0xfe00000000000001ULL,
0xfe00000000000003ULL,
0xfe00000000000007ULL,
0xfe0000000000000fULL,
0xfe0000000000001fULL,
0xfe0000000000003fULL,
0xfe0000000000007fULL,
0xfe000000000000ffULL,
0xfe000000000001ffULL,
0xfe000000000003ffULL,
0xfe000000000007ffULL,
0xfe00000000000fffULL,
0xfe00000000001fffULL,
0xfe00000000003fffULL,
0xfe00000000007fffULL,
0xfe0000000000ffffULL,
0xfe0000000001ffffULL,
0xfe0000000003ffffULL,
0xfe0000000007ffffULL,
0xfe000000000fffffULL,
0xfe000000001fffffULL,
0xfe000000003fffffULL,
0xfe000000007fffffULL,
0xfe00000000ffffffULL,
0xfe00000001ffffffULL,
0xfe00000003ffffffULL,
0xfe00000007ffffffULL,
0xfe0000000fffffffULL,
0xfe0000001fffffffULL,
0xfe0000003fffffffULL,
0xfe0000007fffffffULL,
0xfe000000ffffffffULL,
0xfe000001ffffffffULL,
0xfe000003ffffffffULL,
0xfe000007ffffffffULL,
0xfe00000fffffffffULL,
0xfe00001fffffffffULL,
0xfe00003fffffffffULL,
0xfe00007fffffffffULL,
0xfe0000ffffffffffULL,
0xfe0001ffffffffffULL,
0xfe0003ffffffffffULL,
0xfe0007ffffffffffULL,
0xfe000fffffffffffULL,
0xfe001fffffffffffULL,
0xfe003fffffffffffULL,
0xfe007fffffffffffULL,
0xfe00ffffffffffffULL,
0xfe01ffffffffffffULL,
0xfe03ffffffffffffULL,
0xfe07ffffffffffffULL,
0xfe0fffffffffffffULL,
0xfe1fffffffffffffULL,
0xfe3fffffffffffffULL,
0xfe7fffffffffffffULL,
0xfeffffffffffffffULL
},
{
0xfc00000000000000ULL,
0xfc00000000000001ULL,
0xfc00000000000003ULL,
0xfc00000000000007ULL,
0xfc0000000000000fULL,
0xfc0000000000001fULL,
0xfc0000000000003fULL,
0xfc0000000000007fULL,
0xfc000000000000ffULL,
0xfc000000000001ffULL,
0xfc000000000003ffULL,
0xfc000000000007ffULL,
0xfc00000000000fffULL,
0xfc00000000001fffULL,
0xfc00000000003fffULL,
0xfc00000000007fffULL,
0xfc0000000000ffffULL,
0xfc0000000001ffffULL,
0xfc0000000003ffffULL,
0xfc0000000007ffffULL,
0xfc000000000fffffULL,
0xfc000000001fffffULL,
0xfc000000003fffffULL,
0xfc000000007fffffULL,
0xfc00000000ffffffULL,
0xfc00000001ffffffULL,
0xfc00000003ffffffULL,
0xfc00000007ffffffULL,
0xfc0000000fffffffULL,
0xfc0000001fffffffULL,
0xfc0000003fffffffULL,
0xfc0000007fffffffULL,
0xfc000000ffffffffULL,
0xfc000001ffffffffULL,
0xfc000003ffffffffULL,
0xfc000007ffffffffULL,
0xfc00000fffffffffULL,
0xfc00001fffffffffULL,
0xfc00003fffffffffULL,
0xfc00007fffffffffULL,
0xfc0000ffffffffffULL,
0xfc0001ffffffffffULL,
0xfc0003ffffffffffULL,
0xfc0007ffffffffffULL,
0xfc000fffffffffffULL,
0xfc001fffffffffffULL,
0xfc003fffffffffffULL,
0xfc007fffffffffffULL,
0xfc00ffffffffffffULL,
0xfc01ffffffffffffULL,
0xfc03ffffffffffffULL,
0xfc07ffffffffffffULL,
0xfc0fffffffffffffULL,
0xfc1fffffffffffffULL,
0xfc3fffffffffffffULL,
0xfc7fffffffffffffULL,
0xfcffffffffffffffULL,
0xfdffffffffffffffULL
},
{
0xf800000000000000ULL,
0xf800000000000001ULL,
0xf800000000000003ULL,
0xf800000000000007ULL,
0xf80000000000000fULL,
0xf80000000000001fULL,
0xf80000000000003fULL,
0xf80000000000007fULL,
0xf8000000000000ffULL,
0xf8000000000001ffULL,
0xf8000000000003ffULL,
0xf8000000000007ffULL,
0xf800000000000fffULL,
0xf800000000001fffULL,
0xf800000000003fffULL,
0xf800000000007fffULL,
0xf80000000000ffffULL,
0xf80000000001ffffULL,
0xf80000000003ffffULL,
0xf80000000007ffffULL,
0xf8000000000fffffULL,
0xf8000000001fffffULL,
0xf8000000003fffffULL,
0xf8000000007fffffULL,
0xf800000000ffffffULL,
0xf800000001ffffffULL,
0xf800000003ffffffULL,
0xf800000007ffffffULL,
0xf80000000fffffffULL,
0xf80000001fffffffULL,
0xf80000003fffffffULL,
0xf80000007fffffffULL,
0xf8000000ffffffffULL,
0xf8000001ffffffffULL,
0xf8000003ffffffffULL,
0xf8000007ffffffffULL,
0xf800000fffffffffULL,
0xf800001fffffffffULL,
0xf800003fffffffffULL,
0xf800007fffffffffULL,
0xf80000ffffffffffULL,
0xf80001ffffffffffULL,
0xf80003ffffffffffULL,
0xf80007ffffffffffULL,
0xf8000fffffffffffULL,
0xf8001fffffffffffULL,
0xf8003fffffffffffULL,
0xf8007fffffffffffULL,
0xf800ffffffffffffULL,
0xf801ffffffffffffULL,
0xf803ffffffffffffULL,
0xf807ffffffffffffULL,
0xf80fffffffffffffULL,
0xf81fffffffffffffULL,
0xf83fffffffffffffULL,
0xf87fffffffffffffULL,
0xf8ffffffffffffffULL,
0xf9ffffffffffffffULL,
0xfbffffffffffffffULL
},
{
0xf000000000000000ULL,
0xf000000000000001ULL,
0xf000000000000003ULL,
0xf000000000000007ULL,
0xf00000000000000fULL,
0xf00000000000001fULL,
0xf00000000000003fULL,
0xf00000000000007fULL,
0xf0000000000000ffULL,
0xf0000000000001ffULL,
0xf0000000000003ffULL,
0xf0000000000007ffULL,
0xf000000000000fffULL,
0xf000000000001fffULL,
0xf000000000003fffULL,
0xf000000000007fffULL,
0xf00000000000ffffULL,
0xf00000000001ffffULL,
0xf00000000003ffffULL,
0xf00000000007ffffULL,
0xf0000000000fffffULL,
0xf0000000001fffffULL,
0xf0000000003fffffULL,
0xf0000000007fffffULL,
0xf000000000ffffffULL,
0xf000000001ffffffULL,
0xf000000003ffffffULL,
0xf000000007ffffffULL,
0xf00000000fffffffULL,
0xf00000001fffffffULL,
0xf00000003fffffffULL,
0xf00000007fffffffULL,
0xf0000000ffffffffULL,
0xf0000001ffffffffULL,
0xf0000003ffffffffULL,
0xf0000007ffffffffULL,
0xf000000fffffffffULL,
0xf000001fffffffffULL,
0xf000003fffffffffULL,
0xf000007fffffffffULL,
0xf00000ffffffffffULL,
0xf00001ffffffffffULL,
0xf00003ffffffffffULL,
0xf00007ffffffffffULL,
0xf0000fffffffffffULL,
0xf0001fffffffffffULL,
0xf0003fffffffffffULL,
0xf0007fffffffffffULL,
0xf000ffffffffffffULL,
0xf001ffffffffffffULL,
0xf003ffffffffffffULL,
0xf007ffffffffffffULL,
0xf00fffffffffffffULL,
0xf01fffffffffffffULL,
0xf03fffffffffffffULL,
0xf07fffffffffffffULL,
0xf0ffffffffffffffULL,
0xf1ffffffffffffffULL,
0xf3ffffffffffffffULL,
0xf7ffffffffffffffULL
},
{
0xe000000000000000ULL,
0xe000000000000001ULL,
0xe000000000000003ULL,
0xe000000000000007ULL,
0xe00000000000000fULL,
0xe00000000000001fULL,
0xe00000000000003fULL,
0xe00000000000007fULL,
0xe0000000000000ffULL,
0xe0000000000001ffULL,
0xe0000000000003ffULL,
0xe0000000000007ffULL,
0xe000000000000fffULL,
0xe000000000001fffULL,
0xe000000000003fffULL,
0xe000000000007fffULL,
0xe00000000000ffffULL,
0xe00000000001ffffULL,
0xe00000000003ffffULL,
0xe00000000007ffffULL,
0xe0000000000fffffULL,
0xe0000000001fffffULL,
0xe0000000003fffffULL,
0xe0000000007fffffULL,
0xe000000000ffffffULL,
0xe000000001ffffffULL,
0xe000000003ffffffULL,
0xe000000007ffffffULL,
0xe00000000fffffffULL,
0xe00000001fffffffULL,
0xe00000003fffffffULL,
0xe00000007fffffffULL,
0xe0000000ffffffffULL,
0xe0000001ffffffffULL,
0xe0000003ffffffffULL,
0xe0000007ffffffffULL,
0xe000000fffffffffULL,
0xe000001fffffffffULL,
0xe000003fffffffffULL,
0xe000007fffffffffULL,
0xe00000ffffffffffULL,
0xe00001ffffffffffULL,
0xe00003ffffffffffULL,
0xe00007ffffffffffULL,
0xe0000fffffffffffULL,
0xe0001fffffffffffULL,
0xe0003fffffffffffULL,
0xe0007fffffffffffULL,
0xe000ffffffffffffULL,
0xe001ffffffffffffULL,
0xe003ffffffffffffULL,
0xe007ffffffffffffULL,
0xe00fffffffffffffULL,
0xe01fffffffffffffULL,
0xe03fffffffffffffULL,
0xe07fffffffffffffULL,
0xe0ffffffffffffffULL,
0xe1ffffffffffffffULL,
0xe3ffffffffffffffULL,
0xe7ffffffffffffffULL,
0xefffffffffffffffULL
},
{
0xc000000000000000ULL,
0xc000000000000001ULL,
0xc000000000000003ULL,
0xc000000000000007ULL,
0xc00000000000000fULL,
0xc00000000000001fULL,
0xc00000000000003fULL,
0xc00000000000007fULL,
0xc0000000000000ffULL,
0xc0000000000001ffULL,
0xc0000000000003ffULL,
0xc0000000000007ffULL,
0xc000000000000fffULL,
0xc000000000001fffULL,
0xc000000000003fffULL,
0xc000000000007fffULL,
0xc00000000000ffffULL,
0xc00000000001ffffULL,
0xc00000000003ffffULL,
0xc00000000007ffffULL,
0xc0000000000fffffULL,
0xc0000000001fffffULL,
0xc0000000003fffffULL,
0xc0000000007fffffULL,
0xc000000000ffffffULL,
0xc000000001ffffffULL,
0xc000000003ffffffULL,
0xc000000007ffffffULL,
0xc00000000fffffffULL,
0xc00000001fffffffULL,
0xc00000003fffffffULL,
0xc00000007fffffffULL,
0xc0000000ffffffffULL,
0xc0000001ffffffffULL,
0xc0000003ffffffffULL,
0xc0000007ffffffffULL,
0xc000000fffffffffULL,
0xc000001fffffffffULL,
0xc000003fffffffffULL,
0xc000007fffffffffULL,
0xc00000ffffffffffULL,
0xc00001ffffffffffULL,
0xc00003ffffffffffULL,
0xc00007ffffffffffULL,
0xc0000fffffffffffULL,
0xc0001fffffffffffULL,
0xc0003fffffffffffULL,
0xc0007fffffffffffULL,
0xc000ffffffffffffULL,
0xc001ffffffffffffULL,
0xc003ffffffffffffULL,
0xc007ffffffffffffULL,
0xc00fffffffffffffULL,
0xc01fffffffffffffULL,
0xc03fffffffffffffULL,
0xc07fffffffffffffULL,
0xc0ffffffffffffffULL,
0xc1ffffffffffffffULL,
0xc3ffffffffffffffULL,
0xc7ffffffffffffffULL,
0xcfffffffffffffffULL,
0xdfffffffffffffffULL
},
{
0x8000000000000000ULL,
0x8000000000000001ULL,
0x8000000000000003ULL,
0x8000000000000007ULL,
0x800000000000000fULL,
0x800000000000001fULL,
0x800000000000003fULL,
0x800000000000007fULL,
0x80000000000000ffULL,
0x80000000000001ffULL,
0x80000000000003ffULL,
0x80000000000007ffULL,
0x8000000000000fffULL,
0x8000000000001fffULL,
0x8000000000003fffULL,
0x8000000000007fffULL,
0x800000000000ffffULL,
0x800000000001ffffULL,
0x800000000003ffffULL,
0x800000000007ffffULL,
0x80000000000fffffULL,
0x80000000001fffffULL,
0x80000000003fffffULL,
0x80000000007fffffULL,
0x8000000000ffffffULL,
0x8000000001ffffffULL,
0x8000000003ffffffULL,
0x8000000007ffffffULL,
0x800000000fffffffULL,
0x800000001fffffffULL,
0x800000003fffffffULL,
0x800000007fffffffULL,
0x80000000ffffffffULL,
0x80000001ffffffffULL,
0x80000003ffffffffULL,
0x80000007ffffffffULL,
0x8000000fffffffffULL,
0x8000001fffffffffULL,
0x8000003fffffffffULL,
0x8000007fffffffffULL,
0x800000ffffffffffULL,
0x800001ffffffffffULL,
0x800003ffffffffffULL,
0x800007ffffffffffULL,
0x80000fffffffffffULL,
0x80001fffffffffffULL,
0x80003fffffffffffULL,
0x80007fffffffffffULL,
0x8000ffffffffffffULL,
0x8001ffffffffffffULL,
0x8003ffffffffffffULL,
0x8007ffffffffffffULL,
0x800fffffffffffffULL,
0x801fffffffffffffULL,
0x803fffffffffffffULL,
0x807fffffffffffffULL,
0x80ffffffffffffffULL,
0x81ffffffffffffffULL,
0x83ffffffffffffffULL,
0x87ffffffffffffffULL,
0x8fffffffffffffffULL,
0x9fffffffffffffffULL,
0xbfffffffffffffffULL
},
{
0x0ULL,
0x1ULL,
0x3ULL,
0x7ULL,
0xfULL,
0x1fULL,
0x3fULL,
0x7fULL,
0xffULL,
0x1ffULL,
0x3ffULL,
0x7ffULL,
0xfffULL,
0x1fffULL,
0x3fffULL,
0x7fffULL,
0xffffULL,
0x1ffffULL,
0x3ffffULL,
0x7ffffULL,
0xfffffULL,
0x1fffffULL,
0x3fffffULL,
0x7fffffULL,
0xffffffULL,
0x1ffffffULL,
0x3ffffffULL,
0x7ffffffULL,
0xfffffffULL,
0x1fffffffULL,
0x3fffffffULL,
0x7fffffffULL,
0xffffffffULL,
0x1ffffffffULL,
0x3ffffffffULL,
0x7ffffffffULL,
0xfffffffffULL,
0x1fffffffffULL,
0x3fffffffffULL,
0x7fffffffffULL,
0xffffffffffULL,
0x1ffffffffffULL,
0x3ffffffffffULL,
0x7ffffffffffULL,
0xfffffffffffULL,
0x1fffffffffffULL,
0x3fffffffffffULL,
0x7fffffffffffULL,
0xffffffffffffULL,
0x1ffffffffffffULL,
0x3ffffffffffffULL,
0x7ffffffffffffULL,
0xfffffffffffffULL,
0x1fffffffffffffULL,
0x3fffffffffffffULL,
0x7fffffffffffffULL,
0xffffffffffffffULL,
0x1ffffffffffffffULL,
0x3ffffffffffffffULL,
0x7ffffffffffffffULL,
0xfffffffffffffffULL,
0x1fffffffffffffffULL,
0x3fffffffffffffffULL,
0x7fffffffffffffffULL
}
};

} // namespace sc_dt


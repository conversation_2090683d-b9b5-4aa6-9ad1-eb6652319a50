#ifndef COM_LIN_HPP
#define COM_LIN_HPP

#include <systemc.h>
#include <boost/asio.hpp>
#include <array>
#include <string>
#include <iostream>
#include "message.pb.h"
#include "Lin_Adapter_Wrapper.h"

using boost::asio::ip::udp;

/**
 * @brief Module giao tiếp LIN - Xử lý truyền nhận dữ liệu qua giao thức LIN
 * 
 * Module này chịu trách nhiệm:
 * - Nhận dữ liệu từ UDP và chuyển đổi thành frame LIN
 * - Gửi dữ liệu LIN qua UDP
 * - Đóng gói/mở gói frame LIN đầy đủ
 */
SC_MODULE(Com_Lin) {
    // ===== PORTS =====
    sc_out<uint8_t> lintx;              // Đầu ra truyền dữ liệu LIN
    sc_in<uint8_t> linrx;               // Đ<PERSON><PERSON> vào nhận dữ liệu LIN
    sc_in<bool> ack;                    // Tín hiệu xác nhận từ receiver
    sc_out<bool> data_ready;            // Tín hiệu báo dữ liệu sẵn sàng
    
    // Ports cho giao tiếp với BMS Software
    sc_out<bool> sbc_bms_ack;           // ACK từ SBC đến BMS
    sc_in<bool> sbc_bms_data_ready;     // Data ready từ BMS đến SBC
    
    // ===== INTERNAL VARIABLES =====
    uint8_t tx_frame[9];                // Frame LIN để truyền (9 bytes)
    uint8_t rx_frame[9];                // Frame LIN nhận được (9 bytes)
    uint8_t full_lin_frame[11];         // Frame LIN đầy đủ (11 bytes)
    
    // UDP communication
    boost::asio::io_context io_ctx;
    udp::socket socket_;
    udp::endpoint remote_ep;
    
    // ===== METHODS =====
    
    /**
     * @brief Thread nhận dữ liệu từ UDP và chuyển đổi thành LIN frame
     * 
     * Chức năng:
     * - Lắng nghe dữ liệu từ UDP port 9000
     * - Parse protobuf message
     * - Chuyển đổi thành LIN frame và truyền qua SystemC ports
     */
    void udp_receive_thread();
    
    /**
     * @brief Thread gửi dữ liệu LIN qua UDP
     * 
     * Chức năng:
     * - Nhận dữ liệu từ SystemC ports
     * - Đóng gói thành protobuf message
     * - Gửi qua UDP đến port 9001
     */
    void udp_send_thread();
    
    /**
     * @brief Bridge function để kết nối SystemC với UDP send thread
     */
    void sc_udp_send_bridge();
    
    // Constructor
    SC_CTOR(Com_Lin);
};

#endif // COM_LIN_HPP
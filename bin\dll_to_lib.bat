﻿setlocal

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

set "DLLName=%~n1"
set "FullDLL=%~1"

if "%DLLName%"=="" (
    echo Using: dll_to_lib.bat yourfile.dll
    goto :eof
)

where dumpbin >nul 2>nul
if errorlevel 1 (
    echo Can not find dumpbin. Run script from Visual Studio Developer Command Prompt.
    goto :eof
)

echo Create file DEF from DLL...
dumpbin /exports "%FullDLL%" > "%DLLName%_exports.txt"

echo LIBRARY %DLLName%.dll > "%DLLName%.def"
echo EXPORTS >> "%DLLName%.def"
for /f "skip=19 tokens=4" %%A in ('findstr /R "^[ ]*[0-9]*[ ]*[_A-Za-z?@]" "%DLLName%_exports.txt"') do (
    echo %%A >> "%DLLName%.def"
)

echo Create file LIB from DEF...
lib /def:"%DLLName%.def" /out:"%DLLName%.lib" /machine:x86

echo Successful. File .lib created: %DLLName%.lib

del "%DLLName%.def"
del "%DLLName%_exports.txt"
del "%DLLName%.exp"

endlocal

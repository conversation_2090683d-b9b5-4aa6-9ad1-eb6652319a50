#ifndef BMS_ECU_HPP
#define BMS_ECU_HPP

#include <fmu4cpp/fmu_base.hpp>
#include <systemc.h>
#include <iostream>

using namespace fmu4cpp;

class BMS_ECU : public fmu_base {
public:
    explicit BMS_ECU(const fmu_data& data);

    bool do_step(double dt) override;
    void reset() override;
    void terminate() override;

    // SystemC Modules
    SC_MODULE(BMS_Software) {
        sc_in<int> in1, in2;
        sc_out<int> out_BMS;
        sc_out<bool> sig_bool_AFE, sig_bool_SBC, CS;

        void SW_application();
        void init_CDD();

        SC_CTOR(BMS_Software) {
            SC_THREAD(SW_application);
        }
    };

    SC_MODULE(AFE) {
        sc_in<bool> bool_in, bool_CS;
        sc_out<int> sig_in1;
        sc_in<double> voltage_in;
        int AFE_heartbeat = 0;

        void compute_output();

        SC_CTOR(AFE) {
            SC_THREAD(compute_output);
        }
    };

    SC_MODULE(SBC) {
        sc_in<bool> bool_in;
        sc_out<int> sig_in2;
        int SBC_heartbeat = 0;

        void compute_output();

        SC_CTOR(SBC) {
            SC_THREAD(compute_output);
        }
    };
   
private:
    double outbms_{};
    double Volt_{};
    std::string lin_frame_{};

    // SystemC module instances and signals
    BMS_Software* bms;
    AFE* afe;
    SBC* sbc;
    sc_signal<int> sig_in1, sig_in2, out_bms;
    sc_signal<bool> sig_bool_afe, sig_bool_sbc, sig_CS;
    sc_signal<double> sig_voltage;
};

#endif // BMS_ECU_HPPW
﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="UserMacros">
    <SYSTEMC_VERSION>3.0.2</SYSTEMC_VERSION>
  </PropertyGroup>
  <PropertyGroup>
    <IntDir>$(Configuration)\</IntDir>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <BuildLogFile>$(IntDir)$(MSBuildProjectName).log</BuildLogFile>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalOptions>/vmg %(AdditionalOptions)</AdditionalOptions>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>SC_INCLUDE_FX;SC_ENABLE_ASSERTIONS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DisableSpecificWarnings>4244;4267;4996</DisableSpecificWarnings>
      <UndefinePreprocessorDefinitions>interface;%(UndefinePreprocessorDefinitions)</UndefinePreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <BuildMacro Include="SYSTEMC_VERSION">
      <Value>$(SYSTEMC_VERSION)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
  </ItemGroup>
</Project>

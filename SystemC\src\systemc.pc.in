#  Licensed to Accellera Systems Initiative Inc. (Accellera) under one or
#  more contributor license agreements.  See the NOTICE file distributed
#  with this work for additional information regarding copyright ownership.
#  Accellera licenses this file to you under the Apache License, Version 2.0
#  (the "License"); you may not use this file except in compliance with the
#  License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
#  implied.  See the License for the specific language governing
#  permissions and limitations under the License.
#
# -------------------------------------------------------------------------
#
# systemc.pc.in --
#    pkg-config definition file (template) for SystemC
#    (http://www.freedesktop.org/wiki/Software/pkg-config/)
#
# Author: <PERSON>, OFFIS, 2013-05-07
#
# Note: The "real" definition (systemc.pc) is generated by "configure"
#       during the build configuration.
#
# -------------------------------------------------------------------------
#
TARGET_ARCH=@TARGET_ARCH@
prefix=@prefix@
exec_prefix=@exec_prefix@
libarchdir=@libdir@@LIB_ARCH_SUFFIX@
includedir=@includedir@

Name: @PACKAGE_NAME@
Description: Accellera @PACKAGE_NAME@ proof-of-concept library
Version: @PACKAGE_VERSION@
URL: @PACKAGE_URL@
Libs: -L${libarchdir} -l@PACKAGE@
Libs.private: @PKGCONFIG_LDPRIV@
Cflags: @PKGCONFIG_CFLAGS@ @PKGCONFIG_DEFINES@ -I${includedir}

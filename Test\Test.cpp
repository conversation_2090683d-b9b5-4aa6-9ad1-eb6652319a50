﻿#include <systemc.h>
#include <boost/asio.hpp>
#include <thread>
#include <string>
#include <iostream>
#include "message.pb.h"

#include "Lin_Adapter_Wrapper.h"
#include "BCC_Wrapper_Transmit.h"
#include "Application_Wrapper.h"
#include "BCC_modeling.h"

#include "hal_bcc.h"

using boost::asio::ip::udp;
 
const int LIN_FRAME_SIZE = 9;
const int FULL_LIN_FRAME_SIZE = 11;
const int LIN_DATA_SIZE = 8;

const int AFE_FRAME_SIZE = 6;
 
// ===== SBC MODULE =====
SC_MODULE(SBC) {
    sc_out<uint8_t> lintx;      
    sc_in<uint8_t> linrx;      
 
    sc_in<bool> ack;
 
    sc_out<bool> data_ready;        
 
    sc_out<bool> sbc_bms_ack;
    sc_in<bool> sbc_bms_data_ready;
 
    uint8_t SBC_lintxframe[LIN_FRAME_SIZE];
    uint8_t SBC_linrxframe[LIN_FRAME_SIZE];
 
    uint8_t full_lin_frame[FULL_LIN_FRAME_SIZE];
 
    boost::asio::io_context io_ctx;
    udp::socket socket_;
    udp::endpoint remote_ep;
    bool sending = false;
 
    void udp_receive_thread() {
        udp::endpoint sender_ep;
        std::array<char, 1024> recv_buf;
 
        while (1) {
            wait(5, SC_US);
            std::cout << sc_time_stamp() << " [SBC] Start SBC : \n"  << std::endl;
            std::cout << sc_time_stamp() << " [SBC] Waiting LIN frame :"  << std::endl;
            boost::system::error_code ec;
            size_t bytes = socket_.receive_from(boost::asio::buffer(recv_buf), sender_ep, 0, ec);
            if (ec) continue;
            example::LinMessage msg;
           
            if (msg.ParseFromArray(recv_buf.data(), bytes)) {
                std::string s = msg.data();
                // Force string to exactly 9 bytes
                if (s.size() < LIN_FRAME_SIZE) s.append(LIN_FRAME_SIZE - s.size(), '\0');
                if (s.size() > LIN_FRAME_SIZE) s.resize(LIN_FRAME_SIZE);
           
                std::cout << sc_time_stamp() << " [SBC] Received from UDP: " <<
                sender_ep.address().to_string() << ":" << sender_ep.port() << " ";
                for (auto c : s)  std::cout << std::hex << (unsigned int)(unsigned char)c << " ";
                std::cout << std::dec << std::endl;
                memcpy(SBC_linrxframe, s.data(), LIN_FRAME_SIZE * sizeof(uint8_t));
 
            }
            
            Lin_Adapter_Packfullframe(SBC_linrxframe, LIN_FRAME_SIZE, full_lin_frame);
 
            for (int i = 0; i < FULL_LIN_FRAME_SIZE; ++i) {
                lintx.write(full_lin_frame[i]);
                data_ready.write(true); 
                do { wait(ack->value_changed_event()); } while (!ack.read());
 
                data_ready.write(false);
 
                do { wait(ack->value_changed_event()); } while (ack.read());
            }
            wait(10, SC_US);
        }
    }
 
    void udp_send_thread() {
        while (1) {
            for (int i = 0; i < FULL_LIN_FRAME_SIZE; i++){
                do { wait(sbc_bms_data_ready->value_changed_event()); } while (!sbc_bms_data_ready.read());
 
                full_lin_frame[i] = linrx.read();
                sbc_bms_ack.write(true);
 
                do { wait(sbc_bms_data_ready->value_changed_event()); } while (sbc_bms_data_ready.read());
 
                sbc_bms_ack.write(false);
            }
 
            Lin_Adapter_UnPackfullframe(SBC_lintxframe, LIN_FRAME_SIZE, full_lin_frame);
             
            std::string s(LIN_FRAME_SIZE, '\0');
            memcpy(&s[0], SBC_lintxframe, LIN_FRAME_SIZE);
            
            if (s.size() < LIN_FRAME_SIZE) s.append(LIN_FRAME_SIZE - s.size(), '\0');
            if (s.size() > LIN_FRAME_SIZE) s.resize(LIN_FRAME_SIZE);
            example::LinMessage msg;
            msg.set_data(s);
            std::string out;
            msg.SerializeToString(&out);
            boost::system::error_code ec;
            remote_ep = udp::endpoint(boost::asio::ip::make_address("127.0.0.1"), 9001);
            socket_.send_to(boost::asio::buffer(out), remote_ep, 0, ec);
 
            if (!ec) {
                std::cout << sc_time_stamp() << " [SBC] Sent to UDP: " << remote_ep.address().to_string()
                << ":" << remote_ep.port() << " ";
                for (auto c : s) std::cout << std::hex << (unsigned int)(unsigned char)c << " ";
                std::cout << std::dec << std::endl;
            }
        }
    }
    SC_CTOR(SBC)
        : socket_(io_ctx, udp::endpoint(udp::v4(), 9000))
    {
       
        SC_THREAD(udp_receive_thread);
        SC_THREAD(sc_udp_send_bridge);
    }
 
    void sc_udp_send_bridge() {
        while (true) {
            wait(1, SC_US);
            udp_send_thread();
        }
    }
};
 
 
// ===== AFE MODULE =====
SC_MODULE(AFE) {
    sc_in<bool> CS;
    sc_out<bool> ack;
    sc_out<uint8_t> spi_tx; 
    sc_in<uint8_t> spi_rx;
 
    uint8_t Tx_frame[AFE_FRAME_SIZE];
    uint8_t Rx_frame[AFE_FRAME_SIZE];
 
    bool get_sensor = false;
    bool udp_data_received = false;
 
    sc_in<bool> data_ready;
 
    boost::asio::io_context io_ctx;
    udp::socket socket_;
 
    double last_volt[AFE_FRAME_SIZE];
    double last_thermal[AFE_FRAME_SIZE];
 
    void udp_receive_thread() {
        udp::endpoint sender_ep;
        std::array<char, 1024> recv_buf;
 
        while (1) {
            wait(1, SC_NS);
            if (get_sensor && !udp_data_received) {
                std::cout << sc_time_stamp() << " [AFE] Waiting data sensors :" << std::endl;
 
                boost::system::error_code ec;
                size_t bytes = socket_.receive_from(boost::asio::buffer(recv_buf), sender_ep, 0, ec);
                if (!ec) {
                    example::AFEData msg;
                    if (msg.ParseFromArray(recv_buf.data(), bytes)) {
                        for (size_t i = 0; i < AFE_FRAME_SIZE && i < msg.volt_size(); ++i)
                            last_volt[i] = msg.volt(i);
                        std::cout << sc_time_stamp() << " [AFE] Received Voltages: ";
                        for (auto v : last_volt) std::cout << v << " ";
                        std::cout << "" << std::endl;
                        for (size_t j = 0; j < AFE_FRAME_SIZE && j < msg.thermal_size(); ++j)
                            last_thermal[j] = msg.thermal(j);
                        for (auto v : last_thermal) std::cout << " Temperature : " << v << std::endl;
                        udp_data_received = true;
                    }
                }
                set_cellsVoltage(last_volt);
                set_cellsTemp(last_thermal);
            }
        }
    }
    void spi_emulation() {
        while (1) {
            wait(1, SC_US);
            if (CS.read()) {
                // processing here
                get_sensor = true;
                udp_data_received = false;

                // Wait for UDP data to be received
                while (get_sensor && !udp_data_received) {
                    wait(10, SC_NS);
                }
                              
                for (int i = 0; i < AFE_FRAME_SIZE; ++i) {
                    while (!data_ready.read()) {
                        wait(data_ready->value_changed_event());
                    }
                    Rx_frame[i] = spi_rx.read();
                    spi_tx.write(Tx_frame[i]);
                    ack.write(true);
                    // Wait until data_ready becomes false
                    while (data_ready.read()) {
                        wait(data_ready->value_changed_event());
                    }
                    ack.write(false);
                }
               
                get_sensor = false;
                BCC_processing(Tx_frame, Rx_frame);
            }
        }
    }
    SC_CTOR(AFE)
        : socket_(io_ctx, udp::endpoint(udp::v4(), 9002))
    {
        SC_THREAD(udp_receive_thread);
        SC_THREAD(spi_emulation);
    }
};
 
// ===== BMS SOFTWARE MODULE =====
SC_MODULE(BMS_Software) {
    sc_in<uint8_t> linrx;
    sc_out<uint8_t> lintx;
 
    sc_in<bool> data_ready;
    sc_out<bool> ack_lin;
 
    sc_out<bool> bms_sbc_ready;
    sc_in<bool> bms_sbc_ack_lin;
 
    uint8_t bms_lintxframe[LIN_FRAME_SIZE];
    uint8_t bms_linrxframe[LIN_FRAME_SIZE];
 
    uint8_t afe_spitxframe[AFE_FRAME_SIZE];
    uint8_t afe_spirxframe[AFE_FRAME_SIZE];
 
    double cells_volt;
 
    sc_out<bool> CS;
    sc_in<uint8_t> Spi_rx;     // Changed
    sc_out<uint8_t> Spi_tx;  // Changed
    sc_in<bool> ack_afe;
    sc_out<bool> data_ready_afe;

    void spi_transfer(uint8_t * txframe, uint8_t frame_size, uint8_t * rx_frame) {
        CS.write(true);
        wait(2, SC_US); 

        for (int i = 0; i < frame_size; ++i) {
            Spi_tx.write(txframe[i]);
            rx_frame[i] = Spi_rx.read();
            data_ready_afe.write(true);

            while (!ack_afe.read()) {
                wait(ack_afe->value_changed_event());
            }
            data_ready_afe.write(false); // Clear
            // Wait until ack_afe becomes false
            while (ack_afe.read()) {
                wait(ack_afe->value_changed_event());
            }
        }
        CS.write(false);
    }

    void main_thread() {
        while (1) {
            wait(1, SC_US);
            std::cout << sc_time_stamp() << " [BMS] Start BMS : \n" << std::endl;
 
            for (int i = 0; i < FULL_LIN_FRAME_SIZE; ++i) {
                do { wait(data_ready->value_changed_event()); } while (!data_ready.read());
 
                bms_linrxframe[i] = linrx.read();
                ack_lin.write(true);

                do { wait(data_ready->value_changed_event()); } while (data_ready.read());
 
                ack_lin.write(false);
            }
             
            // Application
            Lin_PayLoad_Wrapping(bms_linrxframe[2], &bms_linrxframe[3], LIN_DATA_SIZE);
            
            // Handle for LpSpi (BCC)
            BCC_PackFrame_Send(0x0001U, MC33772C_ADC_CFG_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, afe_spitxframe);

/*Time 1:*/ spi_transfer(afe_spitxframe, 6, afe_spirxframe);

            uint16_t * regVal;
            uint16_t regValTemp;
            
            regVal = &regValTemp;

            *regVal = ((uint16_t)*((afe_spirxframe) + BCC_MSG_IDX_DATA_H) << 8U) | (uint16_t)*((afe_spirxframe) + BCC_MSG_IDX_DATA_L);
            
            /* Update register value. */
            regValTemp = regValTemp & ~(MC33772C_ADC_CFG_SOC_MASK | MC33772C_ADC_CFG_AVG_MASK);
            regValTemp = regValTemp | (MC33772C_ADC_CFG_SOC(MC33772C_ADC_CFG_SOC_ENABLED_ENUM_VAL) | MC33772C_ADC_CFG_AVG(0) & MC33772C_ADC_CFG_SOC_MASK | MC33772C_ADC_CFG_AVG_MASK);
            
            /* Create frame for writing. */
            BCC_PackFrame_Send(regValTemp, MC33772C_ADC_CFG_OFFSET, BCC_CID_DEV1, BCC_CMD_WRITE, afe_spitxframe);
            
/*Time 2:*/ spi_transfer(afe_spitxframe, 6, afe_spirxframe);

            BCC_PackFrame_Send(0x0001U, MC33772C_MEAS_CELL6_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, afe_spitxframe);

/*Time 3:*/ spi_transfer(afe_spitxframe, 6, afe_spirxframe);

            BCC_PackFrame_Send(0x0001U, MC33772C_MEAS_CELL6_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, afe_spitxframe);

/*Time 4:*/ spi_transfer(afe_spitxframe, 6, afe_spirxframe);
            
            *regVal = ((uint16_t)*((afe_spirxframe) + BCC_MSG_IDX_DATA_H) << 8U) | (uint16_t)*((afe_spirxframe) + BCC_MSG_IDX_DATA_L);
       
            Hal_Bcc_MainFunction ();

            Process_lin_frame ();

            // Application
            Task_Demo_Application();

            // Handle for lin
            Get_lin_response(bms_lintxframe);

            for (int i = 0; i <  FULL_LIN_FRAME_SIZE; i++) {
                lintx.write(bms_lintxframe[i]);
                bms_sbc_ready.write(true);
 
                do { wait(bms_sbc_ack_lin->value_changed_event()); } while (!bms_sbc_ack_lin.read());
 
                bms_sbc_ready.write(false); // Clear
 
                do { wait(bms_sbc_ack_lin->value_changed_event()); } while (bms_sbc_ack_lin.read());
            }           
        }
    }
    SC_CTOR(BMS_Software) {
        SC_THREAD(main_thread);
    }
};
 

// ===== MAIN =====
int sc_main(int argc, char* argv[]) {
    sc_signal<uint8_t> lintx_sig, linrx_sig;
    sc_signal<bool> data_ready_sig, data_ready_sig_afe;
    sc_signal<bool> cs_sig;
    sc_signal<uint8_t> spitx_sig;      // Changed
    sc_signal<uint8_t> spirx_sig;   // Changed
    sc_signal<bool> ack_sig, ack_afe;
 
    sc_signal<bool> ack_bms_sbc;
    sc_signal<bool> data_ready_bms_sbc;
 
    SBC sbc("sbc");
    AFE afe("afe");
    BMS_Software bms("bms");
 
    sbc.lintx(lintx_sig);
    sbc.linrx(linrx_sig);
    sbc.data_ready(data_ready_sig);
    sbc.ack(ack_sig);
    sbc.sbc_bms_data_ready(data_ready_bms_sbc);
    sbc.sbc_bms_ack(ack_bms_sbc);
 
    bms.lintx(linrx_sig);
    bms.linrx(lintx_sig);
    bms.data_ready(data_ready_sig);
    bms.ack_lin(ack_sig);
    bms.bms_sbc_ack_lin(ack_bms_sbc);
    bms.bms_sbc_ready(data_ready_bms_sbc);
 
    bms.CS(cs_sig);
    bms.Spi_tx(spirx_sig);
    bms.Spi_rx(spitx_sig);
    bms.ack_afe(ack_afe);
    bms.data_ready_afe(data_ready_sig_afe);
 
    afe.CS(cs_sig);
    afe.spi_tx(spitx_sig);
    afe.spi_rx(spirx_sig);
    afe.ack(ack_afe);
    afe.data_ready(data_ready_sig_afe);
 
    std::cout << "===== SYSTEMC SIMULATION START =====" << std::endl;
    sc_start();
    std::cout << "===== SYSTEMC SIMULATION DONE =====" << std::endl;
    return 0;
}
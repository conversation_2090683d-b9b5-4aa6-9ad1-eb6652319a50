#include "Com_Lin.hpp"

// Các hằng số cho LIN communication
const int LIN_FRAME_SIZE = 9;
const int FULL_LIN_FRAME_SIZE = 11;
const int LIN_DATA_SIZE = 8;

/**
 * @brief Constructor của Com_Lin module
 * 
 * Khởi tạo:
 * - UDP socket trên port 9000
 * - Các SystemC threads cho UDP receive và send
 */
SC_CTOR(Com_Lin) : socket_(io_ctx, udp::endpoint(udp::v4(), 9000)) {
    // Khởi tạo các SystemC threads
    SC_THREAD(udp_receive_thread);
    SC_THREAD(sc_udp_send_bridge);
}

/**
 * @brief Thread nhận dữ liệu từ UDP và chuyển đổi thành LIN frame
 * 
 * Quy trình hoạt động:
 * 1. <PERSON><PERSON>ng nghe dữ liệu từ UDP port 9000
 * 2. Parse protobuf LinMessage
 * 3. Chuyển đổi thành LIN frame (9 bytes)
 * 4. <PERSON><PERSON><PERSON> gó<PERSON> thành full LIN frame (11 bytes)
 * 5. Truyền từng byte qua SystemC ports với handshaking
 */
void Com_Lin::udp_receive_thread() {
    udp::endpoint sender_ep;
    std::array<char, 1024> recv_buf;
    
    while (1) {
        wait(5, SC_US);  // Chờ 5 microseconds
        
        std::cout << sc_time_stamp() << " [COM_LIN] Bắt đầu chu kỳ nhận LIN frame" << std::endl;
        std::cout << sc_time_stamp() << " [COM_LIN] Đang chờ dữ liệu từ UDP..." << std::endl;
        
        // Nhận dữ liệu từ UDP
        boost::system::error_code ec;
        size_t bytes = socket_.receive_from(boost::asio::buffer(recv_buf), sender_ep, 0, ec);
        
        if (ec) {
            continue;  // Bỏ qua nếu có lỗi
        }
        
        // Parse protobuf message
        example::LinMessage msg;
        if (msg.ParseFromArray(recv_buf.data(), bytes)) {
            std::string data = msg.data();
            
            // Đảm bảo dữ liệu có đúng 9 bytes
            if (data.size() < LIN_FRAME_SIZE) {
                data.append(LIN_FRAME_SIZE - data.size(), '\0');  // Thêm null bytes
            }
            if (data.size() > LIN_FRAME_SIZE) {
                data.resize(LIN_FRAME_SIZE);  // Cắt bớt nếu quá dài
            }
            
            // Log thông tin nhận được
            std::cout << sc_time_stamp() << " [COM_LIN] Nhận từ UDP: " 
                      << sender_ep.address().to_string() << ":" << sender_ep.port() << " ";
            for (auto c : data) {
                std::cout << std::hex << (unsigned int)(unsigned char)c << " ";
            }
            std::cout << std::dec << std::endl;
            
            // Copy dữ liệu vào rx_frame
            memcpy(rx_frame, data.data(), LIN_FRAME_SIZE * sizeof(uint8_t));
        }
        
        // Đóng gói thành full LIN frame (thêm header và checksum)
        Lin_Adapter_Packfullframe(rx_frame, LIN_FRAME_SIZE, full_lin_frame);
        
        // Truyền từng byte của full frame qua SystemC ports
        for (int i = 0; i < FULL_LIN_FRAME_SIZE; ++i) {
            // Gửi byte dữ liệu
            lintx.write(full_lin_frame[i]);
            data_ready.write(true);  // Báo hiệu dữ liệu sẵn sàng
            
            // Chờ ACK từ receiver
            do { 
                wait(ack->value_changed_event()); 
            } while (!ack.read());
            
            // Clear data_ready signal
            data_ready.write(false);
            
            // Chờ ACK được clear
            do { 
                wait(ack->value_changed_event()); 
            } while (ack.read());
        }
        
        wait(10, SC_US);  // Chờ trước chu kỳ tiếp theo
    }
}

/**
 * @brief Thread gửi dữ liệu LIN qua UDP
 * 
 * Quy trình hoạt động:
 * 1. Nhận từng byte của full LIN frame từ SystemC ports
 * 2. Mở gói thành LIN frame (9 bytes)
 * 3. Đóng gói thành protobuf message
 * 4. Gửi qua UDP đến port 9001
 */
void Com_Lin::udp_send_thread() {
    while (1) {
        // Nhận full LIN frame từ SystemC ports
        for (int i = 0; i < FULL_LIN_FRAME_SIZE; i++) {
            // Chờ dữ liệu sẵn sàng từ BMS
            do { 
                wait(sbc_bms_data_ready->value_changed_event()); 
            } while (!sbc_bms_data_ready.read());
            
            // Đọc byte dữ liệu
            full_lin_frame[i] = linrx.read();
            sbc_bms_ack.write(true);  // Gửi ACK
            
            // Chờ data_ready được clear
            do { 
                wait(sbc_bms_data_ready->value_changed_event()); 
            } while (sbc_bms_data_ready.read());
            
            sbc_bms_ack.write(false);  // Clear ACK
        }
        
        // Mở gói full frame thành LIN frame (9 bytes)
        Lin_Adapter_UnPackfullframe(tx_frame, LIN_FRAME_SIZE, full_lin_frame);
        
        // Chuyển đổi thành string để gửi qua UDP
        std::string data(LIN_FRAME_SIZE, '\0');
        memcpy(&data[0], tx_frame, LIN_FRAME_SIZE);
        
        // Đảm bảo dữ liệu có đúng kích thước
        if (data.size() < LIN_FRAME_SIZE) {
            data.append(LIN_FRAME_SIZE - data.size(), '\0');
        }
        if (data.size() > LIN_FRAME_SIZE) {
            data.resize(LIN_FRAME_SIZE);
        }
        
        // Tạo protobuf message
        example::LinMessage msg;
        msg.set_data(data);
        std::string serialized_data;
        msg.SerializeToString(&serialized_data);
        
        // Gửi qua UDP
        boost::system::error_code ec;
        remote_ep = udp::endpoint(boost::asio::ip::make_address("127.0.0.1"), 9001);
        socket_.send_to(boost::asio::buffer(serialized_data), remote_ep, 0, ec);
        
        if (!ec) {
            // Log thông tin gửi thành công
            std::cout << sc_time_stamp() << " [COM_LIN] Gửi đến UDP: " 
                      << remote_ep.address().to_string() << ":" << remote_ep.port() << " ";
            for (auto c : data) {
                std::cout << std::hex << (unsigned int)(unsigned char)c << " ";
            }
            std::cout << std::dec << std::endl;
        }
    }
}

/**
 * @brief Bridge function để kết nối SystemC với UDP send thread
 * 
 * Chức năng:
 * - Tạo một wrapper để gọi udp_send_thread trong SystemC context
 * - Đảm bảo timing phù hợp với SystemC simulation
 */
void Com_Lin::sc_udp_send_bridge() {
    while (true) {
        wait(1, SC_US);  // Chờ 1 microsecond
        udp_send_thread();
    }
}
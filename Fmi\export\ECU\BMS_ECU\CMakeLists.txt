add_library(<PERSON><PERSON>_ECU SHARED
        "src/BMS_ECU.cpp"
)

set_target_properties(BMS_ECU PROPERTIES
    OUTPUT_NAME "BMS_ECU"
    PREFIX ""

    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/BMS_ECU/binaries/win32"
)

set(outputDir "${CMAKE_BINARY_DIR}/BMS_ECU/binaries/win32") # Only use for target platform win32 for MCU and Canoe

foreach(DLL ${LIB_DIR})
    add_custom_command(TARGET BMS_ECU POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${DLL}
                ${CMAKE_BINARY_DIR}
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${DLL}
                ${outputDir}
    )
endforeach()

target_include_directories(BMS_ECU PUBLIC
    # Include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    # Include APP
    ${APP_incl}/include
    # Include directories for BCC
    ${BCC_incl}/Modeling
    ${BCC_incl}/include
    # Include directories for SBC
    ${SBC_incl}/Lin_Adapter/include
    ${SBC_incl}/Lin_Trcv_fs23/include
    # Include directories for MCAL
    ${MCAL_incl}/inc
    ${MCAL_incl}/board
    ${MCAL_incl}/generate/include
    ${MCAL_incl}/HAL/include
    ${MCAL_incl}/RTD/include
    ${MCAL_incl}/User_Config/include
    ${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/include
    ${MCAL_incl}/BaseNXP_TS_T40D34M30I0R0/header
    ${MCAL_incl}/Platform_TS_T40D34M30I0R0/startup/include
    ${MCAL_incl}/Platform_TS_T40D34M30I0R0/include
)

target_link_libraries (BMS_ECU PRIVATE
    SystemC::systemc 
    ws2_32
) 

if(MSVC)
    target_link_libraries (BMS_ECU PRIVATE   
        ${CMAKE_DL_LIBS} 
        "${BMS_LIB}/libBSW.lib"
        "${BMS_LIB}/libASW.lib" 
        # "${BMS_LIB}/libVINFAST_dll.lib"
    )
else()
    target_link_libraries (BMS_ECU PRIVATE 
        ${CMAKE_DL_LIBS} 
        "${BMS_LIB}/libBSW.dll.a"
        "${BMS_LIB}/libASW.dll.a" 
        # "${BMS_LIB}/libVINFAST_dll.dll.a"
    )
endif()


generateFMU("BMS_ECU" "${fmi_version}")

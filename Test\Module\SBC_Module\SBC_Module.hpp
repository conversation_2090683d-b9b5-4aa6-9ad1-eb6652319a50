#ifndef SBC_MODULE_HPP
#define SBC_MODULE_HPP

#include <systemc.h>
#include <iostream>
#include "Com_Lin.hpp"

/**
 * @brief SBC (System Basis Chip) Module - Module quản lý giao tiếp LIN
 * 
 * Module này chịu trách nhiệm:
 * - Tích hợp Com_Lin module để xử lý giao tiếp LIN
 * - Cung cấp interface chuẩn cho BMS Software
 * - Quản lý các tín hiệu điều khiển LIN bus
 * - Đóng vai trò như một gateway giữa external LIN network và internal BMS
 */
SC_MODULE(SBC_Module) {
    // ===== PORTS =====
    // Ports kết nối với BMS Software
    sc_out<uint8_t> lintx;              // Đầu ra truyền dữ liệu LIN đến BMS
    sc_in<uint8_t> linrx;               // Đầu vào nhận dữ liệu LIN từ BMS
    sc_in<bool> ack;                    // Tín hiệu xác nhận từ BMS
    sc_out<bool> data_ready;            // Tín hiệu báo dữ liệu sẵn sàng cho BMS
    
    // Ports cho handshaking với BMS Software
    sc_out<bool> sbc_bms_ack;           // ACK từ SBC đến BMS
    sc_in<bool> sbc_bms_data_ready;     // Data ready từ BMS đến SBC
    
    // ===== INTERNAL COMPONENTS =====
    Com_Lin* com_lin_inst;              // Instance của Com_Lin module
    
    // ===== INTERNAL SIGNALS =====
    // Các signals này được sử dụng nội bộ để kết nối với Com_Lin
    // Trong thiết kế thực tế, có thể cần thêm các logic xử lý khác
    
    // ===== METHODS =====
    
    /**
     * @brief Phương thức khởi tạo và kết nối các components
     * 
     * Chức năng:
     * - Tạo instance của Com_Lin
     * - Kết nối các ports của SBC_Module với Com_Lin
     * - Thiết lập các tín hiệu điều khiển
     */
    void initialize_connections();
    
    /**
     * @brief Phương thức giám sát và quản lý trạng thái LIN bus
     * 
     * Chức năng:
     * - Theo dõi trạng thái hoạt động của LIN communication
     * - Xử lý các lỗi và exception
     * - Cung cấp diagnostic information
     */
    void monitor_lin_status();
    
    // Constructor
    SC_CTOR(SBC_Module);
    
    // Destructor
    ~SBC_Module();
};

#endif // SBC_MODULE_HPP
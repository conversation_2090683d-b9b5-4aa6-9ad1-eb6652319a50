/**
 * @file Test.cpp
 * @brief SystemC Testbench cho BMS (Battery Management System)
 * 
 * File này chứa testbench chính để mô phỏng hệ thống BMS với cấu trúc modular:
 * - SBC_Module: Qu<PERSON>n lý giao tiếp LIN
 * - BCC_Module: <PERSON>u<PERSON>n lý giao tiếp SPI với Battery Cell Controller
 * - SW_Module: Phần mềm BMS chính
 * 
 * Cấu trúc được tổ chức theo hierarchy:
 * Test/
 * ├── Com/
 * │   ├── Com_Lin/     - Module giao tiếp LIN
 * │   └── Com_Spi/     - Module giao tiếp SPI
 * └── Module/
 *     ├── SBC_Module/  - System Basis Chip Module
 *     ├── BCC_Module/  - Battery Cell Controller Module
 *     └── SW_Module/   - Software Module (BMS Logic)
 */

#include <systemc.h>
#include <iostream>

// Include các module headers
#include "SBC_Module.hpp"
#include "BCC_Module.hpp"
#include "SW_Module.hpp"

/**
 * @brief Hàm main của SystemC simulation
 * 
 * Chức năng:
 * - Khởi tạo các modules (SBC, BCC, SW)
 * - Tạo và kết nối các signals
 * - Chạy simulation
 * 
 * @param argc Số lượng arguments
 * @param argv Mảng arguments
 * @return int Exit code
 */
int sc_main(int argc, char* argv[]) {
    std::cout << "====================================================" << std::endl;
    std::cout << "    SYSTEMC BMS SIMULATION - MODULAR ARCHITECTURE    " << std::endl;
    std::cout << "====================================================" << std::endl;
    
    // ===== KHAI BÁO SIGNALS =====
    
    // LIN Communication Signals (giữa SBC và SW modules)
    sc_signal<uint8_t> lin_tx_sig;      // LIN data từ SBC đến SW
    sc_signal<uint8_t> lin_rx_sig;      // LIN data từ SW đến SBC
    sc_signal<bool> lin_data_ready_sig; // Data ready signal cho LIN
    sc_signal<bool> lin_ack_sig;        // ACK signal cho LIN
    
    // LIN Handshaking Signals
    sc_signal<bool> sbc_bms_ack_sig;        // ACK từ SBC đến BMS
    sc_signal<bool> sbc_bms_data_ready_sig; // Data ready từ BMS đến SBC
    
    // SPI Communication Signals (giữa SW và BCC modules)
    sc_signal<bool> spi_cs_sig;         // Chip Select cho SPI
    sc_signal<uint8_t> spi_tx_sig;      // SPI data từ SW đến BCC
    sc_signal<uint8_t> spi_rx_sig;      // SPI data từ BCC đến SW
    sc_signal<bool> spi_ack_sig;        // ACK signal cho SPI
    sc_signal<bool> spi_data_ready_sig; // Data ready signal cho SPI
    
    std::cout << "[TESTBENCH] Signals được khởi tạo thành công" << std::endl;
    
    // ===== KHỞI TẠO MODULES =====
    
    // Khởi tạo SBC Module (System Basis Chip)
    SBC_Module sbc_module("sbc_module");
    std::cout << "[TESTBENCH] SBC Module được khởi tạo" << std::endl;
    
    // Khởi tạo BCC Module (Battery Cell Controller)
    BCC_Module bcc_module("bcc_module");
    std::cout << "[TESTBENCH] BCC Module được khởi tạo" << std::endl;
    
    // Khởi tạo SW Module (BMS Software)
    SW_Module sw_module("sw_module");
    std::cout << "[TESTBENCH] SW Module được khởi tạo" << std::endl;
    
    // ===== KẾT NỐI SBC MODULE =====
    
    // Kết nối LIN communication ports
    sbc_module.lintx(lin_tx_sig);           // SBC gửi dữ liệu LIN
    sbc_module.linrx(lin_rx_sig);           // SBC nhận dữ liệu LIN
    sbc_module.data_ready(lin_data_ready_sig); // SBC báo dữ liệu sẵn sàng
    sbc_module.ack(lin_ack_sig);            // SBC nhận ACK
    
    // Kết nối handshaking ports
    sbc_module.sbc_bms_ack(sbc_bms_ack_sig);        // SBC gửi ACK đến BMS
    sbc_module.sbc_bms_data_ready(sbc_bms_data_ready_sig); // SBC nhận data ready từ BMS
    
    std::cout << "[TESTBENCH] SBC Module ports được kết nối" << std::endl;
    
    // ===== KẾT NỐI SW MODULE =====
    
    // Kết nối LIN communication ports (với SBC)
    sw_module.linrx(lin_tx_sig);            // SW nhận dữ liệu từ SBC
    sw_module.lintx(lin_rx_sig);            // SW gửi dữ liệu đến SBC
    sw_module.data_ready(lin_data_ready_sig); // SW nhận data ready từ SBC
    sw_module.ack_lin(lin_ack_sig);         // SW gửi ACK cho LIN
    
    // Kết nối handshaking ports (với SBC)
    sw_module.bms_sbc_ack_lin(sbc_bms_ack_sig);     // SW nhận ACK từ SBC
    sw_module.bms_sbc_ready(sbc_bms_data_ready_sig); // SW gửi data ready đến SBC
    
    // Kết nối SPI communication ports (với BCC)
    sw_module.CS(spi_cs_sig);               // SW điều khiển Chip Select
    sw_module.Spi_tx(spi_tx_sig);           // SW gửi dữ liệu SPI
    sw_module.Spi_rx(spi_rx_sig);           // SW nhận dữ liệu SPI
    sw_module.ack_BCC(spi_ack_sig);         // SW nhận ACK từ BCC
    sw_module.data_ready_BCC(spi_data_ready_sig); // SW gửi data ready đến BCC
    
    std::cout << "[TESTBENCH] SW Module ports được kết nối" << std::endl;
    
    // ===== KẾT NỐI BCC MODULE =====
    
    // Kết nối SPI communication ports
    bcc_module.CS(spi_cs_sig);              // BCC nhận Chip Select
    bcc_module.spi_tx(spi_rx_sig);          // BCC gửi dữ liệu (slave to master)
    bcc_module.spi_rx(spi_tx_sig);          // BCC nhận dữ liệu (master to slave)
    bcc_module.ack(spi_ack_sig);            // BCC gửi ACK
    bcc_module.data_ready(spi_data_ready_sig); // BCC nhận data ready
    
    std::cout << "[TESTBENCH] BCC Module ports được kết nối" << std::endl;
    
    // ===== THÔNG TIN SIMULATION =====
    
    std::cout << "\n====================================================" << std::endl;
    std::cout << "              SIMULATION CONFIGURATION              " << std::endl;
    std::cout << "====================================================" << std::endl;
    std::cout << "Modules:" << std::endl;
    std::cout << "  - SBC_Module: Quản lý giao tiếp LIN (UDP port 9000/9001)" << std::endl;
    std::cout << "  - BCC_Module: Quản lý giao tiếp SPI (UDP port 9002)" << std::endl;
    std::cout << "  - SW_Module:  Phần mềm BMS chính" << std::endl;
    std::cout << "\nCommunication:" << std::endl;
    std::cout << "  - LIN: SBC <-> SW Module" << std::endl;
    std::cout << "  - SPI: SW <-> BCC Module" << std::endl;
    std::cout << "  - UDP: External communication cho sensor data" << std::endl;
    std::cout << "\nSignals:" << std::endl;
    std::cout << "  - LIN signals: " << lin_tx_sig.name() << ", " << lin_rx_sig.name() << std::endl;
    std::cout << "  - SPI signals: " << spi_tx_sig.name() << ", " << spi_rx_sig.name() << std::endl;
    std::cout << "====================================================\n" << std::endl;
    
    // ===== CHẠY SIMULATION =====
    
    std::cout << "[TESTBENCH] Bắt đầu SystemC simulation..." << std::endl;
    std::cout << "[TESTBENCH] Để dừng simulation, sử dụng Ctrl+C" << std::endl;
    std::cout << "\n===== SYSTEMC SIMULATION START =====" << std::endl;
    
    try {
        // Chạy simulation (vô hạn hoặc đến khi có tín hiệu dừng)
        sc_start();
    }
    catch (const std::exception& e) {
        std::cout << "\n[TESTBENCH] Exception caught: " << e.what() << std::endl;
    }
    
    std::cout << "\n===== SYSTEMC SIMULATION DONE =====" << std::endl;
    std::cout << "[TESTBENCH] Simulation kết thúc thành công" << std::endl;
    
    return 0;
}

/**
 * @brief Thông tin về architecture
 * 
 * KIẾN TRÚC MODULAR BMS:
 * 
 * 1. SBC_MODULE (System Basis Chip Module):
 *    - Chứa Com_Lin module
 *    - Xử lý giao tiếp LIN với external network
 *    - UDP communication (port 9000/9001)
 *    - Cung cấp interface chuẩn cho SW module
 * 
 * 2. BCC_MODULE (Battery Cell Controller Module):
 *    - Chứa Com_Spi module
 *    - Mô phỏng BCC hardware
 *    - Nhận sensor data qua UDP (port 9002)
 *    - Xử lý SPI communication với SW module
 * 
 * 3. SW_MODULE (Software Module):
 *    - Phần mềm BMS chính
 *    - Xử lý logic nghiệp vụ
 *    - Giao tiếp với cả SBC và BCC modules
 *    - Thực hiện thuật toán quản lý pin
 * 
 * LUỒNG DỮ LIỆU:
 * External LIN -> SBC_Module -> SW_Module -> BCC_Module -> Sensors
 *                     ^                           |
 *                     |                           v
 *                 UDP 9000/9001              UDP 9002
 * 
 * ƯU ĐIỂM CỦA KIẾN TRÚC:
 * - Modular: Dễ bảo trì và mở rộng
 * - Reusable: Các module có thể tái sử dụng
 * - Testable: Dễ dàng test từng module riêng biệt
 * - Scalable: Có thể thêm modules mới dễ dàng
 * - Clear separation: Tách biệt rõ ràng giữa communication và logic
 */
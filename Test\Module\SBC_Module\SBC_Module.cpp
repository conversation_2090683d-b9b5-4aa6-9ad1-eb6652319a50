#include "SBC_Module.hpp"

/**
 * @brief Constructor của SBC_Module
 * 
 * Khởi tạo:
 * - Tạo instance của Com_Lin module
 * - Thiết lập các kết nối ports
 * - Khởi tạo monitoring thread
 */
SC_CTOR(SBC_Module) {
    // Tạo instance của Com_Lin module
    com_lin_inst = new Com_Lin("com_lin_instance");
    
    // Kết nối các ports của SBC_Module với Com_Lin
    initialize_connections();
    
    // Khởi tạo monitoring thread
    SC_THREAD(monitor_lin_status);
    
    std::cout << "[SBC_MODULE] SBC Module được khởi tạo thành công" << std::endl;
}

/**
 * @brief Destructor của SBC_Module
 * 
 * Giải phóng tài nguyên:
 * - Xóa instance của Com_Lin
 * - Cleanup các resources
 */
SBC_Module::~SBC_Module() {
    if (com_lin_inst) {
        delete com_lin_inst;
        com_lin_inst = nullptr;
    }
    std::cout << "[SBC_MODULE] SBC Module được giải phóng" << std::endl;
}

/**
 * @brief Phương thức khởi tạo và kết nối các components
 * 
 * Chức năng:
 * - Kết nối trực tiếp các ports của SBC_Module với Com_Lin
 * - Thiết lập signal routing
 * - Đảm bảo tính toàn vẹn của kết nối
 */
void SBC_Module::initialize_connections() {
    // Kết nối các ports LIN data
    com_lin_inst->lintx(lintx);         // Kết nối output LIN TX
    com_lin_inst->linrx(linrx);         // Kết nối input LIN RX
    
    // Kết nối các ports điều khiển
    com_lin_inst->ack(ack);             // Kết nối ACK signal
    com_lin_inst->data_ready(data_ready); // Kết nối data ready signal
    
    // Kết nối các ports handshaking với BMS
    com_lin_inst->sbc_bms_ack(sbc_bms_ack);           // ACK từ SBC đến BMS
    com_lin_inst->sbc_bms_data_ready(sbc_bms_data_ready); // Data ready từ BMS
    
    std::cout << "[SBC_MODULE] Các kết nối ports đã được thiết lập" << std::endl;
}

/**
 * @brief Phương thức giám sát và quản lý trạng thái LIN bus
 * 
 * Chức năng:
 * - Theo dõi hoạt động của LIN communication
 * - Phát hiện và báo cáo lỗi
 * - Cung cấp thông tin diagnostic
 * - Quản lý power management cho LIN transceiver
 */
void SBC_Module::monitor_lin_status() {
    // Biến đếm để theo dõi hoạt động
    int activity_counter = 0;
    bool previous_data_ready = false;
    bool previous_ack = false;
    
    while (true) {
        wait(100, SC_US);  // Kiểm tra mỗi 100 microseconds
        
        // Theo dõi hoạt động của data_ready signal
        bool current_data_ready = data_ready.read();
        if (current_data_ready != previous_data_ready) {
            if (current_data_ready) {
                activity_counter++;
                std::cout << sc_time_stamp() 
                          << " [SBC_MODULE] LIN Activity detected - Frame #" 
                          << activity_counter << std::endl;
            }
            previous_data_ready = current_data_ready;
        }
        
        // Theo dõi hoạt động của ACK signal
        bool current_ack = ack.read();
        if (current_ack != previous_ack) {
            if (current_ack) {
                std::cout << sc_time_stamp() 
                          << " [SBC_MODULE] ACK received from BMS" << std::endl;
            }
            previous_ack = current_ack;
        }
        
        // Kiểm tra trạng thái handshaking với BMS
        static bool previous_bms_data_ready = false;
        bool current_bms_data_ready = sbc_bms_data_ready.read();
        if (current_bms_data_ready != previous_bms_data_ready) {
            if (current_bms_data_ready) {
                std::cout << sc_time_stamp() 
                          << " [SBC_MODULE] BMS ready to send data" << std::endl;
            }
            previous_bms_data_ready = current_bms_data_ready;
        }
        
        // Báo cáo định kỳ về trạng thái hoạt động
        static int report_counter = 0;
        report_counter++;
        if (report_counter >= 10000) {  // Báo cáo mỗi 1 giây (10000 * 100us)
            std::cout << sc_time_stamp() 
                      << " [SBC_MODULE] Status Report - Total LIN frames processed: " 
                      << activity_counter << std::endl;
            report_counter = 0;
        }
        
        // Kiểm tra timeout cho các operations
        // Trong thực tế, có thể cần implement timeout detection
        // và error recovery mechanisms
    }
}
#ifndef BCC_MODULE_HPP
#define BCC_MODULE_HPP

#include <systemc.h>
#include <iostream>
#include "Com_Spi.hpp"

/**
 * @brief BCC (Battery Cell Controller) Module - Module quản lý giao tiếp SPI với BCC chip
 * 
 * Module này chịu trách nhiệm:
 * - Tích hợp Com_Spi module để xử lý giao tiếp SPI
 * - Cung cấp interface chuẩn cho BMS Software
 * - Quản lý các tín hiệu điều khiển SPI bus
 * - Xử lý dữ liệu sensor từ battery cells
 * - <PERSON><PERSON><PERSON> vai trò như một bridge giữa BMS và physical BCC hardware
 */
SC_MODULE(BCC_Module) {
    // ===== PORTS =====
    // Ports kết nối với BMS Software (SPI Master)
    sc_in<bool> CS;                     // Chip Select signal từ BMS
    sc_out<bool> ack;                   // Tín hiệu xá<PERSON> nhận đến BMS
    sc_out<uint8_t> spi_tx;             // Dữ liệu truyền từ BCC đến BMS
    sc_in<uint8_t> spi_rx;              // Dữ liệu nhận từ BMS
    sc_in<bool> data_ready;             // Tín hiệu báo dữ liệu sẵn sàng từ BMS
    
    // ===== INTERNAL COMPONENTS =====
    Com_Spi* com_spi_inst;              // Instance của Com_Spi module
    
    // ===== INTERNAL VARIABLES =====
    // Các biến để theo dõi trạng thái và performance
    int transaction_count;              // Đếm số lượng SPI transactions
    bool module_active;                 // Trạng thái hoạt động của module
    
    // ===== METHODS =====
    
    /**
     * @brief Phương thức khởi tạo và kết nối các components
     * 
     * Chức năng:
     * - Tạo instance của Com_Spi
     * - Kết nối các ports của BCC_Module với Com_Spi
     * - Thiết lập các tín hiệu điều khiển
     */
    void initialize_connections();
    
    /**
     * @brief Phương thức giám sát và quản lý trạng thái SPI bus
     * 
     * Chức năng:
     * - Theo dõi trạng thái hoạt động của SPI communication
     * - Đếm số lượng transactions
     * - Phát hiện và báo cáo lỗi
     * - Cung cấp diagnostic information
     */
    void monitor_spi_status();
    
    /**
     * @brief Phương thức quản lý power và performance
     * 
     * Chức năng:
     * - Quản lý power states của BCC
     * - Tối ưu hóa performance
     * - Xử lý sleep/wake modes
     */
    void power_management();
    
    /**
     * @brief Phương thức xử lý diagnostic và error handling
     * 
     * Chức năng:
     * - Phát hiện lỗi communication
     * - Xử lý error recovery
     * - Cung cấp diagnostic data
     */
    void diagnostic_handler();
    
    // Constructor
    SC_CTOR(BCC_Module);
    
    // Destructor
    ~BCC_Module();
};

#endif // BCC_MODULE_HPP
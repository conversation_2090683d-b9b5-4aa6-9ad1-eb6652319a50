![SystemC][logo]
# SystemC Class Library

## About SystemC

  SystemC addresses the need for a system design and verification language that
  spans hardware and software.  It is a language built as an ANSI C++ class
  library created for design and verification.  Users worldwide are applying
  SystemC to system-level modeling, abstract analog/mixed-signal modeling,
  architectural exploration, performance modeling, software development,
  functional verification, and high-level synthesis.  The SystemC API and
  its semantics are developed by the [Accellera Systems Initiative][1] and
  formally standardized and governed by the IEEE in the
  [IEEE Std. 1666-2023][2] standard.

  This version of SystemC is the **reference implementation** provided by
  the [Accellera Systems Initiative][1] and is mainly developed by the following
  Accellera Working Groups

  * [SystemC Language Working Group][3] (LWG)
  * [SystemC Datatypes Working Group][4] (SDTWG)

  In the event of discrepancies between the behavior of this implementation and
  statements made in the [IEEE Std. 1666-2023][2] standard, the IEEE standard
  shall be taken to be definitive.

  **If you would like to contribute to this repository,
    please check the [CONTRIBUTING.md](CONTRIBUTING.md) file.**

---

### Licensing and Copyright

  See the separate [LICENSE](LICENSE) and [NOTICE](NOTICE) files to determine
  your rights and responsiblities for using SystemC.

### User Documentation

  The main documentation of SystemC is the _[IEEE Std. 1666-2023][2]
  Standard SystemC Language Reference Manual_.
  You can find additional documentation for this release in the
  [docs](docs) directory, some of which refers to older versions.

### Installation

  See the separate [INSTALL.md](INSTALL.md) file that provides system
  information and installation instructions.

### Release Notes

  See the separate [RELEASENOTES.md](RELEASENOTES.md) file that provides
  up-to-date information about this release of SystemC.

### SystemC Community

  * [SystemC community website](https://systemc.org/)

  * [Community discussion forum](https://forums.accellera.org/forum/9-systemc/)


## About Accellera SystemC Working Groups

  Accellera's SystemC [Language Working Group][3] (SystemC LWG) is one of the
  Accellera Working Groups that produce effective and efficient Electronic
  Design Automation (EDA) and Intellectual Property (IP) standards for today's
  advanced IC designs and embedded systems.  The SystemC LWG is responsible for
  the definition and development of the SystemC and TLM core languages, the
  foundation on which all other SystemC libraries and functionality are built.
  Participants of both working groups include member companies and industry
  contributors.  Technical contributors typically have many years of practical
  experience with IC and system design as well as developing and using EDA
  tools.

## About Accellera Systems Initiative

  Accellera Systems Initiative is an independent, not-for profit organization
  dedicated to create, support, promote and advance system-level design,
  modeling and verification standards for use by the worldwide electronics
  industry.  The organization accelerates standards development and, as part of
  its ongoing partnership with the IEEE, its standards are contributed to the
  IEEE Standards Association for formal standardization and ongoing change
  control.  For more information, please visit [www.accellera.org][1].  Find out
  more about [membership][5].  Follow [@accellera][6] on Twitter or to comment,
  please use `#accellera`.  

_Accellera, Accellera Systems Initiative and SystemC are trademarks of
 Accellera Systems Initiative Inc.  All other trademarks and trade names
 are the property of their respective owners._

[1]: https://accellera.org
[2]: https://ieeexplore.ieee.org/document/10246125
[3]: https://accellera.org/activities/working-groups/systemc-language
[4]: https://accellera.org/activities/working-groups/systemc-language/systemc-datatypes
[5]: https://accellera.org/about/join/
[6]: https://twitter.com/accellera
[logo]: https://www.accellera.org/images/about/policies/logos/logo_systemc.gif

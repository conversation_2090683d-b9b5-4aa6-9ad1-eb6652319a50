#ifndef COM_SPI_HPP
#define COM_SPI_HPP

#include <systemc.h>
#include <boost/asio.hpp>
#include <array>
#include <string>
#include <iostream>
#include "message.pb.h"
#include "BCC_modeling.h"

using boost::asio::ip::udp;

/**
 * @brief Module giao tiếp SPI - Xử lý truyền nhận dữ liệu qua giao thức SPI
 * 
 * Module này chịu trách nhiệm:
 * - Mô phỏng giao tiếp SPI với BCC (Battery Cell Controller)
 * - Nhận dữ liệu sensor từ UDP
 * - <PERSON><PERSON> lý các lệnh đọc/ghi register của BCC
 * - Cung cấp dữ liệu voltage và temperature cho BMS
 */
SC_MODULE(Com_Spi) {
    // ===== PORTS =====
    sc_in<bool> CS;                     // Chip Select signal từ master
    sc_out<bool> ack;                   // Tín hiệu xác nhận
    sc_out<uint8_t> spi_tx;             // Dữ liệu truyền từ slave đến master
    sc_in<uint8_t> spi_rx;              // Dữ liệu nhận từ master
    sc_in<bool> data_ready;             // Tín hiệu báo dữ liệu sẵn sàng từ master
    
    // ===== INTERNAL VARIABLES =====
    uint8_t tx_frame[6];                // Frame SPI để truyền (6 bytes)
    uint8_t rx_frame[6];                // Frame SPI nhận được (6 bytes)
    
    // Flags điều khiển
    bool get_sensor;                    // Flag báo cần lấy dữ liệu sensor
    bool udp_data_received;             // Flag báo đã nhận dữ liệu từ UDP
    
    // UDP communication cho sensor data
    boost::asio::io_context io_ctx;
    udp::socket socket_;
    
    // Dữ liệu sensor
    double last_volt[6];                // Dữ liệu voltage của 6 cells
    double last_thermal[6];             // Dữ liệu temperature của 6 sensors
    
    // ===== METHODS =====
    
    /**
     * @brief Thread nhận dữ liệu sensor từ UDP
     * 
     * Chức năng:
     * - Lắng nghe dữ liệu sensor từ UDP port 9002
     * - Parse protobuf BCCData message
     * - Cập nhật dữ liệu voltage và temperature
     * - Gọi các hàm set_cellsVoltage và set_cellsTemp
     */
    void udp_receive_thread();
    
    /**
     * @brief Thread mô phỏng giao tiếp SPI
     * 
     * Chức năng:
     * - Chờ tín hiệu CS (Chip Select) từ master
     * - Thực hiện handshaking protocol với master
     * - Trao đổi dữ liệu SPI frame (6 bytes)
     * - Gọi BCC_processing để xử lý dữ liệu
     */
    void spi_emulation();
    
    // Constructor
    SC_CTOR(Com_Spi);
};

#endif // COM_SPI_HPP
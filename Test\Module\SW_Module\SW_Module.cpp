#include "SW_Module.hpp"

// Các hằng số cho communication
const int LIN_FRAME_SIZE = 9;
const int FULL_LIN_FRAME_SIZE = 11;
const int LIN_DATA_SIZE = 8;
const int BCC_FRAME_SIZE = 6;

/**
 * @brief Constructor của SW_Module
 * 
 * Khởi tạo:
 * - <PERSON><PERSON><PERSON> biến trạng thái
 * - Buffers cho communication
 * - SystemC threads
 */
SC_CTOR(SW_Module) {
    // Khởi tạo các biến trạng thái
    system_initialized = false;
    cycle_count = 0;
    cells_volt = 0.0;
    
    // Khởi tạo buffers
    memset(bms_lintxframe, 0, sizeof(bms_lintxframe));
    memset(bms_linrxframe, 0, sizeof(bms_linrxframe));
    memset(BCC_spitxframe, 0, sizeof(BCC_spitxframe));
    memset(BCC_spirxframe, 0, sizeof(BCC_spirxframe));
    
    // Khởi tạo SystemC threads
    SC_THREAD(main_thread);
    SC_THREAD(system_monitor);
    
    std::cout << "[SW_MODULE] BMS Software Module được khởi tạo thành công" << std::endl;
}

/**
 * @brief Thread chính xử lý logic BMS
 * 
 * Đây là thread chính thực hiện toàn bộ quy trình xử lý của BMS:
 * 1. Nhận và xử lý dữ liệu LIN
 * 2. Thực hiện các operations với BCC
 * 3. Chạy application logic
 * 4. Gửi response LIN
 */
void SW_Module::main_thread() {
    while (1) {
        wait(1, SC_US);  // Chu kỳ xử lý 1 microsecond
        cycle_count++;
        
        std::cout << sc_time_stamp() << " [SW_MODULE] Bắt đầu chu kỳ BMS #" << cycle_count << std::endl;
        
        // ===== BƯỚC 1: XỬ LÝ LIN INPUT =====
        process_lin_input();
        
        // ===== BƯỚC 2: XỬ LÝ BCC OPERATIONS =====
        process_bcc_operations();
        
        // ===== BƯỚC 3: CHẠY APPLICATION LOGIC =====
        run_application_logic();
        
        // ===== BƯỚC 4: GỬI LIN RESPONSE =====
        send_lin_response();
        
        std::cout << sc_time_stamp() << " [SW_MODULE] Hoàn thành chu kỳ BMS #" << cycle_count << std::endl;
    }
}

/**
 * @brief Phương thức xử lý LIN communication input
 * 
 * Nhận full LIN frame từ SBC và extract payload data
 */
void SW_Module::process_lin_input() {
    std::cout << sc_time_stamp() << " [SW_MODULE] Bắt đầu nhận LIN frame từ SBC" << std::endl;
    
    // Nhận full LIN frame (11 bytes) từ SBC
    for (int i = 0; i < FULL_LIN_FRAME_SIZE; ++i) {
        // Chờ dữ liệu sẵn sàng từ SBC
        do { 
            wait(data_ready->value_changed_event()); 
        } while (!data_ready.read());
        
        // Đọc byte dữ liệu
        bms_linrxframe[i] = linrx.read();
        ack_lin.write(true);  // Gửi ACK
        
        std::cout << sc_time_stamp() << " [SW_MODULE] Nhận LIN byte[" << i 
                  << "]: 0x" << std::hex << (unsigned int)bms_linrxframe[i] << std::dec << std::endl;
        
        // Chờ data_ready được clear
        do { 
            wait(data_ready->value_changed_event()); 
        } while (data_ready.read());
        
        ack_lin.write(false);  // Clear ACK
    }
    
    // Xử lý payload LIN (byte 2 là PID, bytes 3-10 là data)
    std::cout << sc_time_stamp() << " [SW_MODULE] Xử lý LIN payload - PID: 0x" 
              << std::hex << (unsigned int)bms_linrxframe[2] << std::dec << std::endl;
    
    // Gọi hàm wrapper để xử lý payload
    Lin_PayLoad_Wrapping(bms_linrxframe[2], &bms_linrxframe[3], LIN_DATA_SIZE);
}

/**
 * @brief Phương thức thực hiện SPI transfer với BCC
 * 
 * @param txframe Buffer chứa dữ liệu gửi
 * @param frame_size Kích thước frame
 * @param rx_frame Buffer nhận dữ liệu
 */
void SW_Module::spi_transfer(uint8_t* txframe, uint8_t frame_size, uint8_t* rx_frame) {
    std::cout << sc_time_stamp() << " [SW_MODULE] Bắt đầu SPI transfer (" << (int)frame_size << " bytes)" << std::endl;
    
    // Kích hoạt Chip Select
    CS.write(true);
    wait(2, SC_US);  // Setup time
    
    // Trao đổi dữ liệu từng byte
    for (int i = 0; i < frame_size; ++i) {
        // Gửi dữ liệu và đọc response
        Spi_tx.write(txframe[i]);
        rx_frame[i] = Spi_rx.read();
        data_ready_BCC.write(true);
        
        std::cout << sc_time_stamp() << " [SW_MODULE] SPI byte[" << i 
                  << "] TX: 0x" << std::hex << (unsigned int)txframe[i]
                  << ", RX: 0x" << (unsigned int)rx_frame[i] << std::dec << std::endl;
        
        // Chờ ACK từ BCC
        while (!ack_BCC.read()) {
            wait(ack_BCC->value_changed_event());
        }
        
        data_ready_BCC.write(false);  // Clear data ready
        
        // Chờ ACK được clear
        while (ack_BCC.read()) {
            wait(ack_BCC->value_changed_event());
        }
    }
    
    // Deactivate Chip Select
    CS.write(false);
    std::cout << sc_time_stamp() << " [SW_MODULE] Hoàn thành SPI transfer" << std::endl;
}

/**
 * @brief Phương thức xử lý BCC operations
 * 
 * Thực hiện các operations với BCC:
 * 1. Đọc ADC configuration
 * 2. Cấu hình ADC
 * 3. Đọc measurement data
 */
void SW_Module::process_bcc_operations() {
    std::cout << sc_time_stamp() << " [SW_MODULE] Bắt đầu BCC operations" << std::endl;
    
    uint16_t regVal;
    uint16_t regValTemp;
    
    // ===== OPERATION 1: ĐỌC ADC CONFIGURATION =====
    std::cout << sc_time_stamp() << " [SW_MODULE] Đọc ADC configuration register" << std::endl;
    BCC_PackFrame_Send(0x0001U, MC33772C_ADC_CFG_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, BCC_spitxframe);
    spi_transfer(BCC_spitxframe, BCC_FRAME_SIZE, BCC_spirxframe);
    
    // Extract register value từ response
    regVal = ((uint16_t)BCC_spirxframe[BCC_MSG_IDX_DATA_H] << 8U) | (uint16_t)BCC_spirxframe[BCC_MSG_IDX_DATA_L];
    std::cout << sc_time_stamp() << " [SW_MODULE] ADC Config value: 0x" << std::hex << regVal << std::dec << std::endl;
    
    // ===== OPERATION 2: CẤU HÌNH ADC =====
    std::cout << sc_time_stamp() << " [SW_MODULE] Cấu hình ADC register" << std::endl;
    regValTemp = regVal;
    
    // Clear các bits cần modify
    regValTemp = regValTemp & ~(MC33772C_ADC_CFG_SOC_MASK | MC33772C_ADC_CFG_AVG_MASK);
    
    // Set new configuration
    regValTemp = regValTemp | (MC33772C_ADC_CFG_SOC(MC33772C_ADC_CFG_SOC_ENABLED_ENUM_VAL) | 
                              MC33772C_ADC_CFG_AVG(0) & MC33772C_ADC_CFG_SOC_MASK | MC33772C_ADC_CFG_AVG_MASK);
    
    // Gửi write command
    BCC_PackFrame_Send(regValTemp, MC33772C_ADC_CFG_OFFSET, BCC_CID_DEV1, BCC_CMD_WRITE, BCC_spitxframe);
    spi_transfer(BCC_spitxframe, BCC_FRAME_SIZE, BCC_spirxframe);
    
    // ===== OPERATION 3: ĐỌC MEASUREMENT DATA (2 lần) =====
    std::cout << sc_time_stamp() << " [SW_MODULE] Đọc measurement data (lần 1)" << std::endl;
    BCC_PackFrame_Send(0x0001U, MC33772C_MEAS_CELL6_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, BCC_spitxframe);
    spi_transfer(BCC_spitxframe, BCC_FRAME_SIZE, BCC_spirxframe);
    
    std::cout << sc_time_stamp() << " [SW_MODULE] Đọc measurement data (lần 2)" << std::endl;
    BCC_PackFrame_Send(0x0001U, MC33772C_MEAS_CELL6_OFFSET, BCC_CID_DEV1, BCC_CMD_READ, BCC_spitxframe);
    spi_transfer(BCC_spitxframe, BCC_FRAME_SIZE, BCC_spirxframe);
    
    // Extract final measurement value
    regVal = ((uint16_t)BCC_spirxframe[BCC_MSG_IDX_DATA_H] << 8U) | (uint16_t)BCC_spirxframe[BCC_MSG_IDX_DATA_L];
    std::cout << sc_time_stamp() << " [SW_MODULE] Measurement value: 0x" << std::hex << regVal << std::dec << std::endl;
    
    std::cout << sc_time_stamp() << " [SW_MODULE] Hoàn thành BCC operations" << std::endl;
}

/**
 * @brief Phương thức chạy application logic
 * 
 * Thực hiện các thuật toán và logic nghiệp vụ của BMS
 */
void SW_Module::run_application_logic() {
    std::cout << sc_time_stamp() << " [SW_MODULE] Chạy application logic" << std::endl;
    
    // Gọi HAL BCC main function
    Hal_Bcc_MainFunction();
    
    // Xử lý LIN frame
    Process_lin_frame();
    
    // Chạy demo application
    Task_Demo_Application();
    
    std::cout << sc_time_stamp() << " [SW_MODULE] Hoàn thành application logic" << std::endl;
}

/**
 * @brief Phương thức gửi LIN response
 * 
 * Chuẩn bị và gửi response LIN về SBC
 */
void SW_Module::send_lin_response() {
    std::cout << sc_time_stamp() << " [SW_MODULE] Chuẩn bị LIN response" << std::endl;
    
    // Lấy LIN response data
    Get_lin_response(bms_lintxframe);
    
    std::cout << sc_time_stamp() << " [SW_MODULE] Gửi LIN response đến SBC" << std::endl;
    
    // Gửi full LIN frame (11 bytes) đến SBC
    for (int i = 0; i < FULL_LIN_FRAME_SIZE; i++) {
        lintx.write(bms_lintxframe[i]);
        bms_sbc_ready.write(true);
        
        std::cout << sc_time_stamp() << " [SW_MODULE] Gửi LIN byte[" << i 
                  << "]: 0x" << std::hex << (unsigned int)bms_lintxframe[i] << std::dec << std::endl;
        
        // Chờ ACK từ SBC
        do { 
            wait(bms_sbc_ack_lin->value_changed_event()); 
        } while (!bms_sbc_ack_lin.read());
        
        bms_sbc_ready.write(false);  // Clear ready signal
        
        // Chờ ACK được clear
        do { 
            wait(bms_sbc_ack_lin->value_changed_event()); 
        } while (bms_sbc_ack_lin.read());
    }
    
    std::cout << sc_time_stamp() << " [SW_MODULE] Hoàn thành gửi LIN response" << std::endl;
}

/**
 * @brief Phương thức giám sát hệ thống
 * 
 * Theo dõi performance và trạng thái của SW module
 */
void SW_Module::system_monitor() {
    int last_cycle_count = 0;
    
    while (true) {
        wait(1, SC_MS);  // Kiểm tra mỗi 1 millisecond
        
        // Tính toán cycle rate
        int cycles_per_ms = cycle_count - last_cycle_count;
        last_cycle_count = cycle_count;
        
        // Báo cáo định kỳ
        static int report_counter = 0;
        report_counter++;
        if (report_counter >= 1000) {  // Báo cáo mỗi 1 giây
            std::cout << sc_time_stamp() 
                      << " [SW_MODULE] Performance Report - Total cycles: " << cycle_count
                      << ", Rate: " << cycles_per_ms << " cycles/ms" << std::endl;
            
            if (!system_initialized && cycle_count > 0) {
                system_initialized = true;
                std::cout << sc_time_stamp() 
                          << " [SW_MODULE] System initialization completed" << std::endl;
            }
            
            report_counter = 0;
        }
        
        // Kiểm tra performance issues
        if (cycles_per_ms > 1000) {
            std::cout << sc_time_stamp() 
                      << " [SW_MODULE] WARNING: High cycle rate detected (" 
                      << cycles_per_ms << " cycles/ms)" << std::endl;
        }
    }
}